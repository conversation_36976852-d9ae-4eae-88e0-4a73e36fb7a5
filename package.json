{"name": "hairloom", "private": true, "version": "0.3.1", "type": "module", "scripts": {"dev": "tsx scripts/with-db.ts -- vite dev --host", "build": "vite build", "postbuild": "echo 'Build completed. Run npm run db:release for database setup.'", "preview": "tsx scripts/with-db.ts -- vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test": "npm run test:integration && npm run test:unit", "test:integration": "playwright test", "test:unit": "vitest", "test:e2e": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test", "test:e2e:headed": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --headed", "test:e2e:debug": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --debug", "test:e2e:ui": "cross-env VITE_WEBAUTHN_ORIGIN=http://localhost:4173 playwright test --ui", "db:mode:docker": "cross-env DB_MODE=docker", "db:mode:embedded": "cross-env DB_MODE=embedded", "db:mode:remote": "cross-env DB_MODE=remote", "db:start": "docker compose up", "db:stop": "docker compose down", "db:embedded:start": "tsx scripts/embedded-postgres.ts start", "db:embedded:stop": "tsx scripts/embedded-postgres.ts stop", "db:generate": "tsx scripts/with-db.ts -- drizzle-kit generate", "db:push": "tsx scripts/with-db.ts -- drizzle-kit push", "db:migrate": "tsx scripts/with-db.ts -- tsx src/lib/server/db/migrate.ts", "db:studio": "tsx scripts/with-db.ts -- drizzle-kit studio", "db:seed": "tsx scripts/with-db.ts -- tsx src/lib/server/db/seed/seed.ts", "db:seed:prod": "tsx scripts/with-db.ts -- tsx src/lib/server/db/seed/seed.prod.ts", "db:release": "npm run db:migrate && npm run db:seed:prod", "db:reset": "tsx scripts/with-db.ts -- tsx src/lib/server/db/reset.ts", "db:reseed": "npm run db:reset && npm run db:migrate && npm run db:seed", "pki:mkcert:localhost": "tsx scripts/generate-mkcert.ts", "pki:mkcert:lan": "tsx scripts/generate-mkcert-auto-lan.ts", "pki:mkcert:lan+localhost": "tsx scripts/generate-mkcert-auto-lan.ts --with-localhost"}, "devDependencies": {"@eslint/compat": "^1.3.2", "@eslint/js": "^9.29.0", "@iconify-json/icon-park-outline": "^1.2.2", "@playwright/test": "^1.53.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.21.5", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@types/node": "^22", "@types/sharp": "^0.31.1", "cross-env": "^10.0.0", "daisyui": "^5.0.50", "dotenv": "^17.2.1", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-svelte": "^3.11.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "3.6.2", "svelte": "^5.34.7", "svelte-check": "^4.2.2", "svelte-confetti": "^2.3.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "unplugin-icons": "^22.2.0", "vite": "^7.1.1", "vitest": "^3.2.4"}, "dependencies": {"@node-rs/argon2": "^2.0.2", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@simplewebauthn/browser": "13.1.2", "@simplewebauthn/server": "13.1.2", "@tailwindcss/vite": "^4.1.11", "@vercel/postgres": "^0.10.0", "@vercel/speed-insights": "^1.2.0", "@xenova/transformers": "^2.17.2", "@zxing/browser": "^0.1.5", "arctic": "^3.7.0", "barcode-detector-polyfill": "^0.1.2", "bip39": "^3.1.0", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.2", "embedded-postgres": "^17.5.0-beta.15", "jose": "^5.10.0", "just-safe-get": "^4.2.0", "just-safe-set": "^4.2.1", "postgres": "^3.4.7", "resend": "6.0.1", "sharp": "^0.34.3", "tailwindcss": "^4.1.11", "zod": "^4.0.16"}}