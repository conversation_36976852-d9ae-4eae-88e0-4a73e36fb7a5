<!--
@component
This component provides a unified login and registration flow.
It prioritizes passkey authentication with a conditional UI (autofill) and falls back to social logins or email-based methods.
-->
<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { signupSchema as baseSignupSchema, loginSchema } from '$lib/schemas/auth';
	import { toast } from '$lib/ui/toast';
	import { startAuthentication } from '@simplewebauthn/browser';
	import type { PublicKeyCredentialRequestOptionsJSON } from '@simplewebauthn/server';
	import { onMount, tick } from 'svelte';
	import { z } from 'zod/v4';
	import PasskeyInfo from './PasskeyInfo.svelte';

	type Props = {
		passkeyOptions: PublicKeyCredentialRequestOptionsJSON | null;
	// Add usage before import, per project rule
	// <PasskeyInfo inline={true} /> used below

		socialProviders: { name: string; url: string }[];
		form?: Record<string, any> | null;
	};

	let { passkeyOptions, socialProviders, form: actionForm }: Props = $props();
	let isPasskeySupported = $state(false);

	const dynamicSocialProviders = $derived(
		socialProviders.map((p) => {
			try {
				// Use a dummy base URL because we only care about pathname and search
				const url = new URL(p.url, 'http://dummybase');
				if (form.values.email) {
					url.searchParams.set('login_hint', form.values.email);
				}
				return { ...p, url: `${url.pathname}${url.search}` };
			} catch (e) {
				// Fallback in case of invalid URL
				return p;
			}
		})
	);

	// Schemas for validation
	const emailSchema = z.object({ email: z.string().email('Please enter a valid email address.') });
	const signupSchema = baseSignupSchema.refine((data) => data.password === data.confirm_password, {
		message: 'Passwords do not match',
		path: ['confirm_password']
	});

	// A single form instance to rule them all
	const form = createRuneForm(
		z.object({
			email: z.string(),
			password: z.string(),
			confirm_password: z.string(),
			first_name: z.string(),
			passkeyResponse: z.string()
		}),
		{
			email: '',
			password: '',
			confirm_password: '',
			first_name: '',
			passkeyResponse: ''
		}
	);

	// Component State
	let isLoading = $state(false);
	let step = $state<'initial' | 'lookup_result' | 'register_with_password'>('initial');
	let lookupResult = $state<{
		userExists: boolean;
		methods: { type: string; provider?: string }[];
	} | null>(null);

	let formElement: HTMLFormElement;
	let emailInput: HTMLInputElement;

	// Reactive effects to handle form action results
	$effect(() => {
		if (!actionForm) return;

		// Handle lookup result
		const lookupAction = actionForm.lookup;
		if (lookupAction) {
			if (lookupAction.success) {
				lookupResult = {
					userExists: lookupAction.userExists,
					methods: lookupAction.methods
				};
				form.values.email = lookupAction.email;
				step = 'lookup_result';
			} else if (lookupAction.errors) {
				form.setErrors(lookupAction.errors);
				toast.error('Please enter a valid email address.');
			} else if (lookupAction.error) {
				toast.error(lookupAction.error);
			}
		}

		// Handle login result
		const loginAction = actionForm.login;
		if (loginAction?.errors) {
			form.setErrors(loginAction.errors);
			toast.error(loginAction.errors.root?.[0] || 'Please check your email and password.');
		}

		// Handle signup result
		const signupAction = actionForm.signup;
		if (signupAction?.errors) {
			form.setErrors(signupAction.errors);
			toast.error('Please correct the errors below to create your account.');
		}

		// Handle passkey result
		const passkeyAction = actionForm.passkey;
		if (passkeyAction?.errors) {
			toast.error(passkeyAction.errors.root?.[0] || 'Passkey login failed.');
		}

		const magicLinkAction = actionForm.magic_link;
		if (magicLinkAction) {
			if(magicLinkAction.success){
				toast.success(magicLinkAction.message);
			} else if (magicLinkAction.error){
				toast.error(magicLinkAction.error);
			} else if (magicLinkAction.errors){
				form.setErrors(magicLinkAction.errors);
			}
		}

		if (step === 'initial') {
			handleConditionalPasskey();
		}
		emailInput?.focus();
	});

	onMount(() => {
		if (passkeyOptions) {
			handleConditionalPasskey();
		}
		// Detect basic passkey support
		try {
			// @ts-ignore
			isPasskeySupported = typeof window !== 'undefined' && !!window.PublicKeyCredential;
		} catch {}
		emailInput?.focus();
	});

	async function handleConditionalPasskey() {
		if (!passkeyOptions) return;
		try {
			const response = await startAuthentication({
				optionsJSON: passkeyOptions,
				useBrowserAutofill: true
			});
			form.values.passkeyResponse = JSON.stringify(response);
			await tick();
			formElement.action = '/login?/passkey';
			formElement.requestSubmit();
		} catch (error) {
			// This error is expected if the user doesn't interact with the conditional UI.
		}
	}

	async function handlePasskeySignUp() {
		// Validate email first
		const result = emailSchema.safeParse({ email: form.values.email });
		if (!result.success) {
			form.setErrors(result.error.flatten().fieldErrors);
			toast.error('Please enter a valid email to continue.');
			return;
		}
		await tick();
		formElement.action = '/login?/registerWithPasskey';
		formElement.requestSubmit();
	}

	async function handlePasskeySignIn() {
		if (!form.values.email) return;
		isLoading = true;
		try {
			const res = await fetch('/api/auth/webauthn/generate-authentication-options', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email: form.values.email })
			});

			if (!res.ok) {
				const err = await res.json();
				throw new Error(err.error || 'Could not get passkey options.');
			}
			const options = await res.json();

			const response = await startAuthentication({ optionsJSON: options });
			form.values.passkeyResponse = JSON.stringify(response);

			await tick();
			formElement.action = '/login?/passkey';
			formElement.requestSubmit();
		} catch (error: any) {
			toast.error(error.message || 'Passkey sign-in failed.');
		} finally {
			isLoading = false;
		}
	}

	function handleFormSubmit(event: MouseEvent, schema: z.ZodSchema<any>) {
		const result = schema.safeParse(form.values);
		if (!result.success) {
			event.preventDefault();
			const newErrors = result.error.flatten().fieldErrors;
			form.setErrors(newErrors);
			const errorCount = Object.keys(newErrors).length;
			toast.error(`Please correct the ${errorCount} error(s) below.`);
		} else {
			form.setErrors({});
		}
	}

</script>

<div class="mx-auto w-full max-w-lg animate-fade-in-scale">
	<RuneForm {form}>
		{#snippet children()}
			<form
				method="POST"
				use:enhance={() => {
					isLoading = true;
					return async ({ result }) => {
						await applyAction(result);
						isLoading = false;
					};
				}}
				bind:this={formElement}
			>
				<input type="hidden" name="passkeyResponse" bind:value={form.values.passkeyResponse} />

				<!-- Enhanced Auth Card -->
				<div class="relative">
					<!-- Background decoration -->
					<div class="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/10 to-accent/10 rounded-3xl blur-xl"></div>

					<div class="relative bg-base-100/90 backdrop-blur-sm border border-base-300/30 rounded-2xl shadow-lg overflow-hidden">
						<!-- Header with gradient -->
						<div class="bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 p-8 text-center">
							<div class="flex justify-center mb-4">
								<div class="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-2xl flex items-center justify-center shadow-lg">
									<span class="text-2xl">🔐</span>
								</div>
							</div>
							<div class="flex items-center gap-3 mb-2">
								<h1 class="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
									{#if step === 'register_with_password'}
										✨ Create Your Account
									{:else}
										🚀 Welcome Back!
									{/if}
								</h1>
								<div class="badge badge-warning badge-sm font-semibold">BETA</div>
							</div>
							<p class="text-base-content/70">
								{#if step === 'register_with_password'}
									Join the community and get started
								{:else}
									Sign in to continue your journey
								{/if}
							</p>
						</div>

						<div class="p-8">
							<!-- Email first -->
							<Field name="email">
								{#snippet children(field)}
									<div class="form-control">
										<label for="email-input" class="label">
											<span class="label-text font-medium flex items-center gap-2">
												<span class="text-lg">📧</span>
												Email Address
											</span>
										</label>
										<div class="relative">
											<input
												id="email-input"
												type="email"
												name="email"
												class="input input-bordered input-lg w-full transition-all duration-300 focus:input-primary focus:scale-[1.02] {step !== 'initial' ? 'input-success' : ''}"
												placeholder="Enter your email address"
												autocomplete="username webauthn"
												bind:value={form.values.email}
												bind:this={emailInput}
												onblur={field.handleBlur}
												disabled={step !== 'initial'}
											/>
											{#if step !== 'initial'}
												<div class="absolute right-3 top-1/2 -translate-y-1/2">
													<span class="text-success text-lg">✓</span>
												</div>
											{/if}
										</div>
										<Errors name="email" />
									</div>
								{/snippet}
							</Field>

							<!-- Progress Steps moved below email -->
							<div class="flex items-center justify-center my-6">
								<div class="flex items-center gap-4">
									<div class="flex items-center gap-2">
										<div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-content font-bold text-sm shadow-lg">
											1
										</div>
										<span class="font-medium text-primary">Identify</span>
									</div>
									<div class="w-12 h-1 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
									<div class="flex items-center gap-2">
										<div class="w-8 h-8 {step === 'lookup_result' || step === 'register_with_password' ? 'bg-secondary text-secondary-content' : 'bg-base-300 text-base-content/50'} rounded-full flex items-center justify-center font-bold text-sm shadow-lg transition-all duration-300">
											2
										</div>
										<span class="font-medium {step === 'lookup_result' || step === 'register_with_password' ? 'text-secondary' : 'text-base-content/50'} transition-colors duration-300">Authenticate</span>
									</div>
								</div>
						</div>

							{#if step === 'initial'}
							<div class="mb-2 flex justify-center">
								<!-- Inline passkey info tooltip -->
								<PasskeyInfo inline={true} />
							</div>

								<!-- Enhanced Social Logins -->
							{#if isPasskeySupported}
								<button
									type="button"
									class="btn btn-lg w-full btn-primary"
									title="Fast, secure, and passwordless"
									onclick={handlePasskeySignUp}
								>
									<span class="text-lg">🔑</span>
									Sign up with a Passkey
								</button>
							{/if}

								<div class="space-y-3 mb-6">
									{#each dynamicSocialProviders as provider}
										<a
											href={provider.url}
											class="btn btn-lg w-full bg-gradient-to-r from-base-200 to-base-300 border-base-300/50 hover:from-primary/10 hover:to-secondary/10 hover:border-primary/30 transition-all duration-300 hover:scale-[1.02] shadow-lg"
										>
											<span class="text-lg">
												{#if provider.name === 'GitHub'}
													🐙
												{:else if provider.name === 'Google'}
													🌈
												{:else}
													🔗
												{/if}
											</span>
											Continue with {provider.name}
										</a>
									{/each}
							<!-- Email first -->


								</div>

								<div class="divider text-base-content/50">
									<span class="bg-base-100 px-4 py-2 rounded-full text-sm font-medium">OR</span>
								</div>
							{/if}



						<!-- Registration with Password -->
						{#if step === 'register_with_password'}
							<Field name="first_name">
								{#snippet children(field)}
									<div>
										<label class="floating-label">
											<input
												id="first-name-input"
												type="text"
												name="first_name"
												class="input input-bordered"
												placeholder="John"
												autocomplete="given-name"
												bind:value={form.values.first_name}
												onblur={field.handleBlur}
												required
											/>
											<span>First Name <span class="text-error">*</span></span>
										</label>
										<Errors name="first_name" />
									</div>
								{/snippet}
							</Field>
							<Field name="password">
								{#snippet children(field)}
									<div>
										<label class="floating-label">
											<input
												id="password-input"
												type="password"
												name="password"
												class="input input-bordered"
												placeholder="••••••••"
												autocomplete="new-password"
												bind:value={form.values.password}
												onblur={field.handleBlur}
												required
											/>
											<span>Password <span class="text-error">*</span></span>
										</label>
										<Errors name="password" />
									</div>
								{/snippet}
							</Field>
							<Field name="confirm_password">
								{#snippet children(field)}
									<div>
										<label class="floating-label">
											<input
												id="confirm-password-input"
												type="password"
												name="confirm_password"
												class="input input-bordered"
												placeholder="••••••••"
												autocomplete="new-password"
												bind:value={form.values.confirm_password}
												onblur={field.handleBlur}
												required
											/>
											<span>Confirm Password <span class="text-error">*</span></span>
										</label>
										<Errors name="confirm_password" />
									</div>
								{/snippet}
							</Field>
							<div class="card-actions mt-6">
								<button
									type="submit"
									class="btn btn-primary w-full"
									formaction="/login?/signup"
									disabled={isLoading}
									onclick={(e) => handleFormSubmit(e, signupSchema)}
								>
									{#if isLoading}
										<span class="loading loading-spinner"></span>
									{/if}
									Create Account
								</button>
							</div>
						{:else}
							<!-- Post-Lookup Step -->
							{#if step === 'lookup_result' && lookupResult}
								<!-- Actions for Existing User -->
								{#if lookupResult.userExists}
									<!-- Password Field -->
									{#if lookupResult.methods.some((m) => m.type === 'password')}
										<Field name="password">
											{#snippet children(field)}
												<div>
													<label class="floating-label">
														<input
															id="password-input"
															type="password"
															name="password"
															class="input input-bordered"
															placeholder="••••••••"
															autocomplete="current-password"
															bind:value={form.values.password}
															onblur={field.handleBlur}
															required
														/>
														<span>Password <span class="text-error">*</span></span>
													</label>
													<Errors name="password" />
												</div>
											{/snippet}
										</Field>
										<div class="card-actions mt-6 flex flex-col gap-2">
											<button
												type="submit"
												formaction="/login?/login"
												class="btn btn-primary w-full"
												disabled={isLoading}
												onclick={(e) => handleFormSubmit(e, loginSchema)}
											>
												{#if isLoading}
													<span class="loading loading-spinner"></span>
												{/if}
												Sign In
											</button>
											<button
												type="submit"
												formaction="/login?/magic-link"
												class="btn btn-ghost w-full"
												disabled={isLoading}
											>
												Email me a link to sign in
											</button>
										</div>
									{/if}

									<div class="divider">OR</div>

									<!-- Passkey Button -->
									{#if lookupResult.methods.some((m) => m.type === 'passkey')}
										<button
											type="button"
											class="btn btn-outline w-full"
											onclick={handlePasskeySignIn}
											disabled={isLoading}
										>
											Sign in with Passkey
										</button>
									{/if}

									<!-- OAuth Messages -->
									{#each lookupResult.methods.filter((m) => m.type === 'oauth') as method}
										{@const provider = dynamicSocialProviders.find(
											(p) => p.name.toLowerCase() === method.provider
										)}
										{#if provider}
											<a href={provider.url} class="btn btn-outline w-full"
												>{`Continue with ${provider.name}`}</a
											>
										{/if}
									{/each}
									<button
										type="button"
										class="btn btn-ghost mt-4 w-full"
										onclick={() => {
											step = 'initial';
											form.setErrors({});
										}}
									>
										Back
									</button>
								{:else}
									<!-- Action for New User -->
									<div class="space-y-4">
										<p>
											Welcome! It looks like you're new here. Continue with a social provider or
											create an account with a password.
										</p>
										<button
											class="btn btn-primary w-full"
											onclick={() => (step = 'register_with_password')}
										>
											Create account with password
										</button>
										<div class="divider">OR</div>
										{#each dynamicSocialProviders as provider}
											<a href={provider.url} class="btn btn-outline w-full"
												>{`Continue with ${provider.name}`}</a
											>
										{/each}
										<button
											type="button"
											class="btn btn-ghost mt-4 w-full"
											onclick={() => {
												step = 'initial';
												form.setErrors({});
											}}
										>
											Back
										</button>
									</div>
								{/if}
							{:else}
								<!-- Enhanced Continue Button -->
								<div class="mt-8">
									<button
										type="submit"
										class="btn btn-lg w-full bg-gradient-to-r from-primary to-secondary text-primary-content border-none shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] disabled:opacity-50"
										formaction="/login?/lookup"
										disabled={isLoading}
										onclick={(e) => handleFormSubmit(e, emailSchema)}
									>
										{#if isLoading}
											<span class="loading loading-spinner loading-md"></span>
											Processing...
										{:else}
											<span class="text-lg">🚀</span>
											Continue
										{/if}
									</button>
								</div>
							{/if}
						{/if}
					</div>
				</div>
			</div>
		</form>
		{/snippet}
	</RuneForm>
</div>
