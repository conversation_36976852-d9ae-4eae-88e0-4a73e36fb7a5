import { createSession, generateSessionToken, setSessionTokenCookie } from '$lib/server/auth';
import { google } from '$lib/server/auth/google';
import { issueSessionJWTs } from '$lib/server/auth/jwt';
import { db } from '$lib/server/db';
import { user_oauth_accounts, users } from '$lib/server/db/schema';
import { error, redirect } from '@sveltejs/kit';
import { OAuth2RequestError } from 'arctic';
import { eq } from 'drizzle-orm';

import type { RequestHandler } from './$types';

export const GET: RequestHandler = async (event) => {
	if (!google) {
		throw error(500, 'Google login is not configured.');
	}

	const { url, cookies } = event;
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');

	const storedState = cookies.get('google_oauth_state');
	const codeVerifier = cookies.get('google_code_verifier');

	if (!code || !state || !storedState || state !== storedState || !codeVerifier) {
		return new Response(null, { status: 400 });
	}

	let tokens, googleUser, existingAccount, userId;

	try {
		tokens = await google.validateAuthorizationCode(code, codeVerifier);
		const googleUserResponse = await fetch('https://openidconnect.googleapis.com/v1/userinfo', {
			headers: {
				Authorization: `Bearer ${tokens.accessToken()}`
			}
		});
		googleUser = await googleUserResponse.json();

		existingAccount = await db.query.user_oauth_accounts.findFirst({
			where: eq(user_oauth_accounts.provider_user_id, googleUser.sub)
		});
	} catch (e) {
		if (e instanceof OAuth2RequestError) {
			return new Response(null, {
				status: 400
			});
		}
		console.error(e);
		return new Response(null, {
			status: 500
		});
	}

	if (existingAccount) {
		const sessionToken = generateSessionToken();
		const session = await createSession(sessionToken, existingAccount.user_id);
		setSessionTokenCookie(event, sessionToken, session.expires_at);
		await issueSessionJWTs(event, existingAccount.user_id, session.id);
		redirect(302, '/app');
	}

	try {
		const existingUser = await db.query.users.findFirst({
			where: eq(users.email, googleUser.email)
		});

		if (existingUser) {
			userId = existingUser.id;
		} else {
			const [newUser] = await db
				.insert(users)
				.values({
					email: googleUser.email,
					first_name: googleUser.given_name,
					last_name: googleUser.family_name,
					profile_photo_url: googleUser.picture,
					email_verified: googleUser.email_verified ?? false
				})
				.returning({ id: users.id });
			userId = newUser.id;
		}

		await db.insert(user_oauth_accounts).values({
			provider: 'google',
			provider_user_id: googleUser.sub,
			user_id: userId,
			email: googleUser.email
		});
	} catch (e) {
		console.error(e);
		return new Response(null, {
			status: 500
		});
	}

	const sessionToken = generateSessionToken();
	const session = await createSession(sessionToken, userId);
	setSessionTokenCookie(event, sessionToken, session.expires_at);
	await issueSessionJWTs(event, userId, session.id);

	redirect(302, '/app');
};

interface GoogleUser {
	sub: string;
	name: string;
	given_name: string;
	family_name: string;
	picture: string;
	email: string;
	email_verified: boolean;
	locale: string;
}
