import { env } from '$env/dynamic/private';
import { DAY_IN_MS } from '$lib/server/constants';
import { db } from '$lib/server/db';
import type { UserSession } from '$lib/server/db/schema';
import * as table from '$lib/server/db/schema';
import { sha256 } from '@oslojs/crypto/sha2';
import { encodeBase64url, encodeHexLowerCase } from '@oslojs/encoding';
import type { RequestEvent } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';

export const sessionCookieName = 'auth-session';
export const webauthnChallengeCookieName = 'webauthn-challenge';

export function generateSessionToken(): string {
	// Use 32 bytes (256 bits) for stronger session tokens
	const bytes = crypto.getRandomValues(new Uint8Array(32));
	const token = encodeBase64url(bytes);
	return token;
}

export async function createSession(token: string, userId: string): Promise<UserSession> {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const sessionToInsert: typeof table.user_sessions.$inferInsert = {
		id: sessionId,
		user_id: userId,
		expires_at: new Date(Date.now() + DAY_IN_MS * 30)
	};
	const [insertedSession] = await db
		.insert(table.user_sessions)
		.values(sessionToInsert)
		.returning();
	return insertedSession;
}

export async function getSessionAndUser(
	sessionId: string
): Promise<{ session: table.UserSession; user: table.User } | null> {
	const result = await db
		.select({
			user: table.users,
			session: table.user_sessions
		})
		.from(table.user_sessions)
		.innerJoin(table.users, eq(table.user_sessions.user_id, table.users.id))
		.where(eq(table.user_sessions.id, sessionId));

	if (result.length === 0) {
		return null;
	}
	return result[0];
}

export async function validateSessionToken(token: string) {
	const sessionId = encodeHexLowerCase(sha256(new TextEncoder().encode(token)));
	const [result] = await db
		.select({
			user: table.users,
			session: table.user_sessions
		})
		.from(table.users)
		.innerJoin(table.user_sessions, eq(table.users.id, table.user_sessions.user_id))
		.where(eq(table.user_sessions.id, sessionId));

	if (!result) {
		return { session: null, user: null };
	}
	const { session, user } = result;

	const sessionExpired = Date.now() >= session.expires_at.getTime();
	if (sessionExpired) {
		await db.delete(table.user_sessions).where(eq(table.user_sessions.id, session.id));
		return { session: null, user: null };
	}

	const renewSessionThreshold = session.expires_at.getTime() - DAY_IN_MS * 15;
	if (Date.now() >= renewSessionThreshold) {
		const newExpiresAt = new Date(Date.now() + DAY_IN_MS * 30);
		await db
			.update(table.user_sessions)
			.set({ expires_at: newExpiresAt })
			.where(eq(table.user_sessions.id, session.id));
		session.expires_at = newExpiresAt;
	}

	return { session, user };
}

export type SessionValidationResult = Awaited<ReturnType<typeof validateSessionToken>>;

export async function invalidateSession(sessionId: string): Promise<void> {
	await db.delete(table.user_sessions).where(eq(table.user_sessions.id, sessionId));
}

export async function invalidateUserSessions(userId: string): Promise<void> {
	await db.delete(table.user_sessions).where(eq(table.user_sessions.user_id, userId));
}

export function setSessionTokenCookie(event: RequestEvent, token: string, expiresAt: Date): void {
	event.cookies.set(sessionCookieName, token, {
		httpOnly: true,
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		sameSite: 'lax',
		expires: expiresAt
	});
}

export function deleteSessionTokenCookie(event: RequestEvent): void {
	event.cookies.set(sessionCookieName, '', {
		httpOnly: true,
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		sameSite: 'lax',
		maxAge: 0
	});
}

export function setWebAuthnChallengeCookie(event: RequestEvent, challenge: string): void {
	event.cookies.set(webauthnChallengeCookieName, challenge, {
		httpOnly: true,
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		sameSite: 'lax',
		maxAge: 60 * 10 // 10 minutes
	});
}

export function getWebAuthnChallengeCookie(event: RequestEvent): string | undefined {
	return event.cookies.get(webauthnChallengeCookieName);
}

export function deleteWebAuthnChallengeCookie(event: RequestEvent): void {
	event.cookies.set(webauthnChallengeCookieName, '', {
		httpOnly: true,
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		sameSite: 'lax',
		maxAge: 0
	});
}

export const HMAC_VERIFIER_ALGORITHM = 'SHA-256';

/**
 * Generates a short-lived OTP and its expiration time.
 * @param digits - The number of digits for the OTP (defaults to 6).
 * @param seconds - The number of seconds until the OTP expires (defaults to 60).
 * @returns An object containing the OTP and its expiration date.
 */
export function generateOtp(digits: number = 6, seconds: number = 60) {
	const min = Math.pow(10, digits - 1);
	const max = Math.pow(10, digits) - 1;

	// Use a cryptographically secure random number generator
	const range = max - min + 1;
	const a = new Uint32Array(1);
	let randomValue;
	do {
		randomValue = crypto.getRandomValues(a)[0];
	} while (randomValue >= Math.floor(4294967296 / range) * range);

	const otp = String(min + (randomValue % range));
	const expiresAt = new Date(Date.now() + seconds * 1000);
	return { otp, expiresAt };
}
