<!--
The ThemeController component provides a user interface for switching between the application's light and dark themes.
It uses a DaisyUI "swap" component, which is a purely CSS-based toggle that leverages a hidden checkbox.
This refactored version directly integrates with the `themeStore` to ensure theme persistence.

- The component is wrapped in a `label` to ensure accessibility, allowing a click on the visual icon to toggle the underlying checkbox.
- The `swap` class enables the theme-switching animation.
- `data-sveltekit-reload` is added to the anchor tag to prevent SvelteKit's router from interfering with the theme change, ensuring a smooth and immediate visual update.
- The `theme-controller` class is the key part of the DaisyUI functionality; it links the checkbox's state to the `data-theme` attribute on the `<html>` element.
- The `value` attributes on the checkbox correspond to the theme names defined in `src/app.css`.
- The icons for sun and moon are SVG elements that are conditionally displayed by the "swap" component.
-->
<script lang="ts">
	import { initialTheme, themeStore, type Theme } from '$lib/stores/themeStore';
	import IconMoon from '~icons/icon-park-outline/moon';
	import IconSun from '~icons/icon-park-outline/sun-one';

	// Get initial value from store, but don't use Svelte 4 subscription syntax
	let currentTheme = $state(initialTheme);

	// Subscribe to store changes and update the reactive state
	$effect(() => {
		const unsubscribe = themeStore.subscribe((theme) => {
			currentTheme = theme;
		});
		return unsubscribe;
	});

	function toggleTheme() {
		const newTheme: Theme = currentTheme === 'hairloomdark' ? 'hairloomlight' : 'hairloomdark';
		themeStore.set(newTheme);
	}
</script>

<label class="swap swap-rotate btn btn-ghost btn-circle">
	<input
		type="checkbox"
		onchange={toggleTheme}
		checked={currentTheme === 'hairloomlight'}
		aria-label="Toggle theme"
	/>

	<!-- sun icon -->
	<IconSun class="swap-on h-6 w-6 fill-current" />

	<!-- moon icon -->
	<IconMoon class="swap-off h-6 w-6 fill-current" />
</label>
