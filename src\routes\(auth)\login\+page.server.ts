import { ERROR_MESSAGES } from '$lib/config';
import { signupSchema as baseSignupSchema, loginSchema } from '$lib/schemas/auth';
import { createSession, generateSessionToken, setSessionTokenCookie } from '$lib/server/auth';
import { checkEmailAvailability, generateMagicLinkToken } from '$lib/server/auth/email';
import {
    createEmailVerificationAttempt,
    sendVerificationEmail
} from '$lib/server/auth/email-verification';
import { issueSessionJWTs } from '$lib/server/auth/jwt';
import { verifyPasswordHash, verifyPasswordStrength } from '$lib/server/auth/password.js';
import { RefillingTokenBucket } from '$lib/server/auth/rate-limit';
import { checkUserAuthMethods, createUser, getUserFromEmail } from '$lib/server/auth/user';
import {
    startAuthentication as startPasskeyAuthentication,
    verifyAuthentication
} from '$lib/server/auth/webauthn';
import { sendEmail } from '$lib/server/utils/email';
import type { AuthenticationResponseJSON } from '@simplewebauthn/server';
import { fail, redirect, type RequestEvent } from '@sveltejs/kit';
import { z } from 'zod/v4';
import type { Actions, PageServerLoad } from './$types.js';

const loginIpBucket = new RefillingTokenBucket(10, 0.5); // 10 tokens, refills 0.5 tokens/sec
const loginEmailBucket = new RefillingTokenBucket(5, 0.1); // 5 tokens, refills 0.1 tokens/sec
const signupIpBucket = new RefillingTokenBucket(3, 10);
const lookupIpBucket = new RefillingTokenBucket(20, 0.5);
const magicLinkIpBucket = new RefillingTokenBucket(3, 0.05); // More restrictive: 3 tokens, refills very slowly
const magicLinkEmailBucket = new RefillingTokenBucket(2, 0.02); // Per-email limit for magic links

const emailSchema = z.object({
	email: z.email()
});

const signupSchema = baseSignupSchema
	.refine(async (data) => await verifyPasswordStrength(data.password), {
		message: 'Password is not strong enough.',
		path: ['password']
	})
	.refine(async (data) => await checkEmailAvailability(data.email), {
		message: 'An account with this email address already exists.',
		path: ['email']
	});

type SignupSchema = z.infer<typeof signupSchema>;

export const load: PageServerLoad = async (event) => {
	if (event.locals.user) {
		redirect(302, '/app');
	}

	try {
		const options = await startPasskeyAuthentication();

		event.cookies.set('webauthn_authentication_challenge', options.challenge, {
			path: '/',
			httpOnly: true,
			secure: event.url.protocol === 'https:',
			sameSite: 'strict',
			maxAge: 60 * 5 // 5 minutes
		});

		return {
			passkeyOptions: options
		};
	} catch (e) {
		console.error('Failed to generate passkey auth options:', e);
		// Don't block login page if passkey fails to load
		return {
			passkeyOptions: null
		};
	}
};

export const actions: Actions = {
	lookup: async ({ request, getClientAddress }) => {
		const ip = getClientAddress();
		if (!(await lookupIpBucket.consume(ip, 1))) {
			return fail(429, { lookup: { error: ERROR_MESSAGES.ACCOUNT_LOCKED } });
		}

		const formData = Object.fromEntries(await request.formData());
		const result = emailSchema.safeParse(formData);

		if (!result.success) {
			return fail(400, {
				lookup: {
					data: formData,
					errors: result.error.flatten().fieldErrors
				}
			});
		}

		const { email } = result.data;

		try {
			const { userExists, methods } = await checkUserAuthMethods(email);
			return {
				lookup: {
					success: true,
					userExists,
					methods,
					email
				}
			};
		} catch (e) {
			console.error('Error during auth lookup:', e);
			return fail(500, { lookup: { error: ERROR_MESSAGES.GENERIC_ERROR } });
		}
	},
	login: async (event) => {
		const { request } = event;
		const formData = Object.fromEntries(await request.formData());
		const validation = loginSchema.safeParse(formData);

		if (!validation.success) {
			return fail(400, {
				login: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}
		const { email, password } = validation.data;

		const ip = event.getClientAddress();

		// Rate limit based on IP and email
		const [ipLimited, emailLimited] = await Promise.all([
			loginIpBucket.consume(ip, 1).then((r) => !r),
			loginEmailBucket.consume(email, 1).then((r) => !r)
		]);

		if (ipLimited || emailLimited) {
			return fail(429, {
				login: {
					data: formData,
					errors: { root: ['Too many login attempts. Please try again later.'] }
				}
			});
		}

		let existingUser;
		try {
			existingUser = await getUserFromEmail(email);
		} catch (e) {
			console.error(e);
			return fail(500, {
				login: {
					data: formData,
					errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] }
				}
			});
		}

		if (!existingUser || !existingUser.hashed_password) {
			return fail(400, {
				login: {
					data: formData,
					errors: { root: ['Incorrect email or password.'] }
				}
			});
		}

		let validPassword;
		try {
			validPassword = await verifyPasswordHash(existingUser.hashed_password, password);
		} catch (e) {
			console.error(e);
			return fail(500, {
				login: {
					data: formData,
					errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] }
				}
			});
		}

		if (!validPassword) {
			return fail(400, {
				login: {
					data: formData,
					errors: { root: ['Incorrect email or password.'] }
				}
			});
		}

		const sessionToken = generateSessionToken();
		const session = await createSession(sessionToken, existingUser.id);
		setSessionTokenCookie(event, sessionToken, session.expires_at);
		await issueSessionJWTs(event, existingUser.id, session.id);

		redirect(302, '/app');
	},
	passkey: async (event) => {
		const { request, cookies } = event;
		const formData = await request.formData();
		const passkeyResponseString = formData.get('passkeyResponse');

		if (typeof passkeyResponseString !== 'string' || !passkeyResponseString) {
			return fail(400, { passkey: { errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] } } });
		}

		const passkeyResponse: AuthenticationResponseJSON = JSON.parse(passkeyResponseString);
		const challenge = cookies.get('webauthn_authentication_challenge');
		cookies.delete('webauthn_authentication_challenge', { path: '/' });

		if (!challenge) {
			return fail(400, { passkey: { errors: { root: ['Missing challenge for passkey login.'] } } });
		}

		let verificationResult;
		try {
			verificationResult = await verifyAuthentication(passkeyResponse, challenge);
		} catch (e) {
			console.error('Passkey login error:', e);
			return fail(500, { passkey: { errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] } } });
		}

		if (verificationResult.verified) {
			const sessionToken = generateSessionToken();
			const session = await createSession(sessionToken, verificationResult.userId);
			setSessionTokenCookie(event, sessionToken, session.expires_at);
			await issueSessionJWTs(event, verificationResult.userId, session.id);
			redirect(302, '/app');
		}

		return fail(400, { passkey: { errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] } } });
	},
	signup: async (event: RequestEvent) => {
		const clientIP = event.getClientAddress();
		if (clientIP !== null && !(await signupIpBucket.check(clientIP, 1))) {
			return fail(429, {
				message: 'Too many requests'
			});
		}

		const { request, url } = event;
		const formData = Object.fromEntries(await request.formData());
		const validation = await signupSchema.safeParseAsync(formData);

		if (!validation.success) {
			return fail(400, {
				signup: {
					data: formData,
					errors: validation.error.flatten().fieldErrors
				}
			});
		}
		const { email, first_name, password } = validation.data;

		if (clientIP !== null) {
			await signupIpBucket.consume(clientIP, 1);
		}

		try {
			const user = await createUser(email, first_name, password);
			const attempt = await createEmailVerificationAttempt(user.id, email);
			if (attempt) {
				await sendVerificationEmail(
					email,
					attempt.plaintextUrlToken,
					attempt.plaintextOtp,
					url.origin
				);
			}
		} catch (e) {
			console.error(e);
			return fail(500, {
				signup: {
					data: formData,
					errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] }
				}
			});
		}
		redirect(303, '/verify-email');
	},
	'magic-link': async (event) => {
		const { request, getClientAddress, url } = event;
		const ip = getClientAddress();
		
		// IP-based rate limiting
		if (!(await magicLinkIpBucket.consume(ip, 1))) {
			return fail(429, { magic_link: { error: 'Too many requests' } });
		}

		const formData = Object.fromEntries(await request.formData());
		const result = emailSchema.safeParse(formData);

		if (!result.success) {
			return fail(400, {
				magic_link: {
					data: formData,
					errors: result.error.flatten().fieldErrors
				}
			});
		}
		const { email } = result.data;

		// Email-based rate limiting
		if (!(await magicLinkEmailBucket.consume(email, 1))) {
			return fail(429, { 
				magic_link: { 
					error: 'Too many magic link requests for this email. Please try again later.' 
				} 
			});
		}

		try {
			const user = await getUserFromEmail(email);
			if (user) {
				const token = await generateMagicLinkToken(user.id);
				const magicLinkUrl = `${url.origin}/login/magic/callback?token=${token}`;
				await sendEmail({
					to: email,
					subject: 'Your Hairloom Sign-In Link',
					text: `Click this link to sign in: ${magicLinkUrl}`,
					html: `<p>Click this link to sign in: <a href="${magicLinkUrl}">Sign In</a></p>`
				});
			}
			// Always return success message to prevent email enumeration
			return {
				magic_link: {
					success: true,
					message: 'If an account with that email exists, a sign-in link has been sent.'
				}
			};
		} catch (e) {
			console.error(e);
			return fail(500, {
				magic_link: { error: ERROR_MESSAGES.GENERIC_ERROR }
			});
		}
	},
	registerWithPasskey: async (event: RequestEvent) => {
		const clientIP = event.getClientAddress();
		if (clientIP !== null && !(await signupIpBucket.check(clientIP, 1))) {
			return fail(429, { message: 'Too many requests' });
		}

		const { request, url } = event;
		const formData = await request.formData();
		const formObject = Object.fromEntries(formData);
		const email = formObject.email as string;
		const first_name = formObject.first_name as string | undefined;

		// Basic validation
		if (!email || !z.email().safeParse(email).success) {
			return fail(400, {
				registerWithPasskey: {
					data: formObject,
					errors: { email: ['A valid email is required.'] }
				}
			});
		}
		if (!(await checkEmailAvailability(email))) {
			return fail(400, {
				registerWithPasskey: {
					data: formObject,
					errors: { email: ['An account with this email address already exists.'] }
				}
			});
		}

		if (clientIP !== null) {
			await signupIpBucket.consume(clientIP, 1);
		}

		try {
			// Create a passwordless user
			const user = await createUser(email, first_name ?? '', null);
			const attempt = await createEmailVerificationAttempt(user.id, email);
			if (attempt) {
				await sendVerificationEmail(
					email,
					attempt.plaintextUrlToken,
					attempt.plaintextOtp,
					url.origin
				);
			}

			// The user will be redirected to verify their email.
			// After verification, they should be guided to the webauthn settings page
			// to complete the passkey registration.
			const sessionToken = generateSessionToken();
			const session = await createSession(sessionToken, user.id);
			setSessionTokenCookie(event, sessionToken, session.expires_at);
			// Also issue JWTs for consistency with other auth methods
			await (await import('$lib/server/auth/jwt')).issueSessionJWTs(event, user.id, session.id);
		} catch (e) {
			console.error(e);
			return fail(500, {
				registerWithPasskey: {
					data: formObject,
					errors: { root: [ERROR_MESSAGES.GENERIC_ERROR] }
				}
			});
		}

		redirect(303, '/verify-email');
	}
};
