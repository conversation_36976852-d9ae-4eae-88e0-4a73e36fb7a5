/**
 * Application-wide configuration constants
 * Centralized configuration for the Hairloom CRM application
 */

// ============================================================================
// TIMING CONSTANTS
// ============================================================================

export const TIMING = {
  // Toast notification durations (milliseconds)
  TOAST_SUCCESS_DURATION: 3000,
  TOAST_ERROR_DURATION: 5000,
  TOAST_INFO_DURATION: 3000,
  TOAST_WARNING_DURATION: 4000,
  
  // Animation durations (milliseconds)
  ANIMATION_FAST: 150,
  ANIMATION_NORMAL: 200,
  ANIMATION_SLOW: 300,
  ANIMATION_MODAL: 300,
  
  // Camera and scanner timeouts (milliseconds)
  CAMERA_INIT_DELAY: 150,
  SCANNER_PROCESSING_DELAY: 100,
  
  // Redirect delays (milliseconds)
  REDIRECT_DELAY: 1000,
  
  // Auto-refresh intervals (milliseconds)
  AUTO_REFRESH_INTERVAL: 30000,
  
  // Debounce delays (milliseconds)
  SEARCH_DEBOUNCE: 300,
  INPUT_DEBOUNCE: 500,
} as const;

// ============================================================================
// SIZE AND DIMENSION CONSTANTS
// ============================================================================

export const DIMENSIONS = {
  // Image upload limits
  MAX_IMAGE_SIZE_KB: 5000,
  MAX_IMAGE_SIZE_COMPACT_KB: 2000,
  MAX_IMAGE_SIZE_GALLERY_KB: 8000,
  
  // Camera dimensions
  CAMERA_WIDTH_IDEAL: 1280,
  CAMERA_HEIGHT_IDEAL: 720,
  MOBILE_CAMERA_HEIGHT: 240,
  
  // Pagination limits
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Form field limits
  MAX_NAME_LENGTH: 255,
  MAX_DESCRIPTION_LENGTH: 1000,
  MAX_BARCODE_LENGTH: 50,
  MAX_EMAIL_LENGTH: 255,
  MAX_PHONE_LENGTH: 20,
  MAX_SUPPLIER_LENGTH: 255,
  
  // Touch target minimum size (mobile accessibility)
  MIN_TOUCH_TARGET: 44, // pixels
} as const;

// ============================================================================
// CURRENCY CONSTANTS
// ============================================================================

export const CURRENCY = {
  // Conversion rates
  CENTS_PER_DOLLAR: 100,
  
  // Display formatting
  DEFAULT_CURRENCY: 'USD',
  DEFAULT_LOCALE: 'en-US',
  
  // Validation limits (in cents)
  MIN_PRICE: 0,
  MAX_PRICE: 999999999, // $9,999,999.99
} as const;

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

export const VALIDATION = {
  // Password requirements
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 255,
  
  // OTP settings
  OTP_EXPIRATION_TIME: 10 * 60 * 1000, // 10 minutes
  
  // Rate limiting
  MAX_LOGIN_ATTEMPTS: 5,
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  
  // File validation
  ACCEPTED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  
  // Quantity limits
  MIN_QUANTITY: 0,
  MAX_QUANTITY: 999999,
  MIN_REORDER_THRESHOLD: 1,
  MAX_REORDER_THRESHOLD: 999999,
} as const;

// ============================================================================
// FEATURE FLAGS
// ============================================================================

export const FEATURES = {
  // Camera and scanning
  ENABLE_CAMERA: true,
  ENABLE_BARCODE_SCANNER: true,
  ENABLE_MANUAL_BARCODE_INPUT: true,
  
  // UI features
  ENABLE_DARK_MODE: true,
  ENABLE_ANIMATIONS: true,
  ENABLE_SOUND_EFFECTS: false,
  
  // Advanced features
  ENABLE_BULK_OPERATIONS: true,
  ENABLE_EXPORT: true,
  ENABLE_ADVANCED_SEARCH: true,
  
  // Development features
  ENABLE_DEBUG_MODE: false,
  ENABLE_PERFORMANCE_MONITORING: true,
} as const;

// ============================================================================
// ENVIRONMENT DETECTION
// ============================================================================

export const ENV = {
  get isDevelopment() {
    return process.env.NODE_ENV === 'development';
  },
  
  get isProduction() {
    return process.env.NODE_ENV === 'production';
  },
  
  get isTest() {
    return process.env.NODE_ENV === 'test';
  },
  
  get isClient() {
    return typeof window !== 'undefined';
  },
  
  get isServer() {
    return typeof window === 'undefined';
  },
} as const;

// ============================================================================
// STORAGE KEYS
// ============================================================================

export const STORAGE_KEYS = {
  // User preferences
  THEME: 'hairloom-theme',
  SIDEBAR_COLLAPSED: 'hairloom-sidebar-collapsed',
  QUICK_ACTION_HISTORY: 'hairloom-quick-action-history',
  
  // Form data
  DRAFT_INVENTORY_ITEM: 'hairloom-draft-inventory-item',
  DRAFT_CLIENT: 'hairloom-draft-client',
  DRAFT_TEMPLATE: 'hairloom-draft-template',
  
  // Session data
  LAST_VISITED_PAGE: 'hairloom-last-visited-page',
  SEARCH_HISTORY: 'hairloom-search-history',
  
  // Camera settings
  CAMERA_FACING_MODE: 'hairloom-camera-facing-mode',
  SCANNER_SETTINGS: 'hairloom-scanner-settings',
} as const;

// ============================================================================
// API CONFIGURATION
// ============================================================================

export const API = {
  // Base configuration
  BASE_URL: '/api',
  TIMEOUT: 30000, // 30 seconds
  
  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
  RETRY_BACKOFF_FACTOR: 2,
  
  // Request limits
  MAX_CONCURRENT_REQUESTS: 10,
  
  // Endpoints (relative to BASE_URL)
  ENDPOINTS: {
    // Authentication
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    

    
    // Clients
    CLIENTS: '/clients',
    CLIENT_NOTES: '/client-notes',
    
    // Templates
    TEMPLATES: '/templates',
    
    // Settings
    SETTINGS: '/settings',
    USER_PREFERENCES: '/user/preferences',
  },
} as const;

// ============================================================================
// ROUTE PATHS
// ============================================================================

export const ROUTES = {
  // Authentication
  LOGIN: '/login',
  REGISTER: '/register',
  LOGOUT: '/logout',
  
  // Main app
  DASHBOARD: '/app',

  // Inventory
  INVENTORY: '/app/inventory',
  INVENTORY_NEW: '/app/inventory/new',
  INVENTORY_ITEM: (id: string) => `/app/inventory/${id}`,
  INVENTORY_UPDATE_MODE: '/app/inventory/update-mode',

  // Clients
  CLIENTS: '/app/clients',
  CLIENTS_NEW: '/app/clients/new',
  CLIENT_DETAIL: (id: string) => `/app/clients/${id}`,
  
  // Templates
  TEMPLATES: '/app/templates',
  TEMPLATE_NEW: '/app/templates/new',
  TEMPLATE_EDIT: (id: string) => `/app/templates/${id}`,
  
  // Settings
  SETTINGS: '/app/settings',
  
  // Error pages
  NOT_FOUND: '/404',
  ERROR: '/error',
} as const;
