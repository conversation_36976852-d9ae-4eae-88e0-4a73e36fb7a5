<!--
  Barcode Scanner Component (modern)
  Uses the native BarcodeDetector API with a WASM polyfill for broad support.
  Maintains the original component API and UI structure.
-->
<script lang="ts">
  import { toast } from '$lib/ui/toast';
  import { onDestroy, onMount } from 'svelte';
  import IconCamera from '~icons/icon-park-outline/camera';
  import IconClose from '~icons/icon-park-outline/close';
  import IconFlashlight from '~icons/icon-park-outline/flashlight';

  // Try to create a native BarcodeDetector (or polyfill). Return null if unavailable.
  async function tryCreateNativeDetector(formats?: string[]) {
    const opts = formats && formats.length ? { formats } : undefined;

    // Native
    const BD = (globalThis as any).BarcodeDetector;
    if (BD && typeof BD === 'function') {
      try {
        return new BD(opts);
      } catch {
        try {
          return new BD();
        } catch {
          // not constructible
        }
      }
    }

    // Polyfill package (best-effort)
    try {
      const mod = await import('barcode-detector-polyfill');
      const { BarcodeDetectorPolyfill } = mod as any;
      if (typeof BarcodeDetectorPolyfill === 'function') {
        try {
          return new BarcodeDetectorPolyfill(opts);
        } catch {
          try {
            return new BarcodeDetectorPolyfill();
          } catch {}
        }
      }
    } catch {}

    return null;
  }

  interface Props {
    onScan: (result: string) => void;
    onError?: (error: string) => void;
    onClose?: () => void;
    isActive?: boolean;
    formats?: string[];
    continuous?: boolean;
    onResult?: (payload: { text: string; format?: string; engine: 'native' | 'zxing'; raw?: unknown }) => void;
    onDebug?: (info: { engine: 'native' | 'zxing'; event: string; data?: unknown }) => void;
  }

  let {
    onScan,
    onError = () => {},
    onClose = () => {},
    isActive = true,
    formats = [],
    continuous = false,
    onResult = () => {},
    onDebug = () => {}
  }: Props = $props();

  let videoElement = $state<HTMLVideoElement | null>(null);
  let detector: any | null = null;
  let rafId: number | null = null;
  let scanning = $state(false);
  let error = $state('');
  let hasCamera = $state(false);
  let stream: MediaStream | null = null;
  let torchSupported = $state(false);
  let torchEnabled = $state(false);
  let cameras = $state<MediaDeviceInfo[]>([]);
  let currentCameraIndex = $state(0);
  let isInitializing = $state(true);

  // Detection engine state
  let engine: 'native' | 'zxing' = 'native';
  let zxingReader: any = null;
  let zxingControls: any = null;

  onMount(async () => {
    try {
      // Basic capability checks
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        error = 'Camera access not supported in this browser';
        toast.error('Camera access is not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.');
        onError(error);
        isInitializing = false;
        return;
      }

      if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
        error = 'Camera access requires HTTPS';
        toast.error('Camera access requires a secure connection (HTTPS). Please access this page over HTTPS.');
        onError(error);
        isInitializing = false;
        return;
      }

      // Request permission and enumerate cameras
      let testStream: MediaStream | null = null;
      try {
        testStream = await navigator.mediaDevices.getUserMedia({ video: true });
        testStream.getTracks().forEach((t) => t.stop());
        testStream = null;

        const devices = await navigator.mediaDevices.enumerateDevices();
        cameras = devices.filter((d) => d.kind === 'videoinput');
        hasCamera = cameras.length > 0;
      } catch (permErr) {
        const errorMessage = (permErr as Error).message;
        if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
          error = 'Camera permission denied';
          toast.error('Camera permission denied. Please allow camera access when prompted and try again.');
        } else if (errorMessage.includes('NotFoundError') || errorMessage.includes('DevicesNotFoundError')) {
          error = 'No camera found on this device';
          toast.error('No camera found. Please ensure your device has a camera and try again.');
        } else {
          error = 'Failed to access camera: ' + errorMessage;
          toast.error('Failed to access camera. Please check your camera permissions and try again.');
        }
        onError(error);
        isInitializing = false;
        return;
      }

      if (!hasCamera) {
        error = 'No camera found on this device';
        toast.error('No camera found. Please ensure your device has a camera and try again.');
        onError(error);
        isInitializing = false;
        return;
      }

      // Initialize detector (native or polyfill). If unavailable, we'll fallback to ZXing during startScanning.
      detector = await tryCreateNativeDetector(formats);

      if (isActive) {
        await startScanning();
      }

      isInitializing = false;
    } catch (err) {
      error = 'Failed to initialize camera: ' + (err as Error).message;
      toast.error('Failed to initialize camera. Please check your camera permissions and try again.');
      onError(error);
      isInitializing = false;
    }
  });

  onDestroy(() => {
    stopScanning();
  });

  async function startScanning() {
    if (!hasCamera || scanning) return;

    try {
      scanning = true;
      error = '';

      // Build constraints preferring current camera or back camera
      const currentCamera = cameras[currentCameraIndex];
      let constraints: MediaStreamConstraints;
      if (currentCamera && currentCamera.deviceId) {
        constraints = {
          video: {
            deviceId: { exact: currentCamera.deviceId },
            facingMode: { ideal: 'environment' },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        };
      } else {
        constraints = {
          video: {
            facingMode: { ideal: 'environment' },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        };
      }

      if (detector) {
        // Native engine
        engine = 'native';

        // Start stream ourselves
        stream = await navigator.mediaDevices.getUserMedia(constraints);
        if (videoElement) {
          videoElement.srcObject = stream;
          try {
            if (videoElement.paused) await videoElement.play();
          } catch (playError) {
            const errorMessage = playError instanceof Error ? playError.message : String(playError);
            if (!errorMessage.includes('already playing')) {
              console.warn('Video play error:', playError);
            }
          }
        }

        // Torch support
        const track = stream.getVideoTracks()[0];
        const capabilities = track.getCapabilities?.() ?? {};
        // @ts-ignore - 'torch' is not in the standard type
        torchSupported = Boolean((capabilities as any).torch);

        // Detection loop
        const scan = async () => {
          if (!scanning || !detector || !videoElement) return;
          try {
            const results = await detector.detect(videoElement);
            if (results && results.length > 0) {
              const first = results[0] as any;
              const value = first.rawValue ?? first.rawText ?? '';
              const format = first.format ?? undefined;
              if (value) {
                onResult?.({ text: value, format, engine, raw: first });
                onScan(value);
                if (!continuous) {
                  stopScanning();
                  return;
                }
              }
            }
          } catch (e) {
            onDebug?.({ engine, event: 'native-detect-error', data: String((e as Error).message ?? e) });
            // ignore transient detection errors
          }
          rafId = requestAnimationFrame(scan);
        };
        rafId = requestAnimationFrame(scan);
      } else {
        // ZXing fallback
        engine = 'zxing';
        const mod = await import('@zxing/browser');
        const { BrowserMultiFormatReader } = mod as any;
        zxingReader = new BrowserMultiFormatReader();

        if (!videoElement) {
          throw new Error('Video element not available');
        }

        zxingControls = await zxingReader.decodeFromConstraints(constraints, videoElement, (result: any, _err: any, controls: any) => {
          if (result) {
            const value = typeof result.getText === 'function' ? result.getText() : String(result.text ?? '');
            const format = typeof result.getBarcodeFormat === 'function' ? result.getBarcodeFormat()?.toString?.() : undefined;
            if (value) {
              onResult?.({ text: value, format, engine, raw: result });
              onScan(value);
              if (!continuous) {
                try { controls?.stop?.(); } catch {}
                stopScanning();
              }
            }
          }
        });

        // Try to set torch capability if API is present
        try {
          // IScannerControls may expose switchTorch
          torchSupported = Boolean((zxingControls as any)?.switchTorch);
        } catch {
          torchSupported = false;
        }

        // Capture the active stream for cleanup
        try {
          // @ts-ignore
          stream = (videoElement as any).srcObject ?? null;
        } catch {}
      }
    } catch (err) {
      scanning = false;
      const errorMessage = (err as Error).message;
      error = 'Failed to start camera: ' + errorMessage;

      if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
        toast.error('Camera permission denied. Please refresh the page and allow camera access when prompted.');
      } else if (errorMessage.includes('NotFoundError') || errorMessage.includes('DevicesNotFoundError')) {
        toast.error('No camera found. Please ensure your device has a working camera.');
      } else if (errorMessage.includes('NotReadableError') || errorMessage.includes('TrackStartError')) {
        toast.error('Camera is already in use by another application. Please close other camera apps and try again.');
      } else if (errorMessage.includes('OverconstrainedError') || errorMessage.includes('ConstraintNotSatisfiedError')) {
        toast.error('Camera settings not supported. Trying with basic settings...');
        setTimeout(() => tryBasicConstraints(), 1000);
        return;
      } else if (errorMessage.includes('NotSupportedError')) {
        toast.error('Camera access not supported in this browser. Please use Chrome, Firefox, or Safari.');
      } else {
        toast.error('Failed to start camera. Please check your camera permissions and try again.');
      }

      onError(error);
    }
  }

  function stopScanning() {
    scanning = false;

    if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }

    if (zxingControls) {
      try { (zxingControls as any).stop?.(); } catch {}
      zxingControls = null;
    }

    if (stream) {
      stream.getTracks().forEach((t) => t.stop());
      stream = null;
    }

    if (videoElement) {
      try {
        videoElement.pause();
      } catch {}
      // @ts-ignore
      videoElement.srcObject = null;
    }

    torchEnabled = false;
  }

  async function tryBasicConstraints() {
    if (!hasCamera || scanning) return;

    try {
      scanning = true;
      error = '';

      stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoElement) {
        videoElement.srcObject = stream;
        try {
          if (videoElement.paused) await videoElement.play();
        } catch {}
      }

      const scan = async () => {
        if (!scanning || !detector || !videoElement) return;
        try {
          const results = await detector.detect(videoElement);
          if (results && results.length > 0) {
            const value = (results[0] as any).rawValue ?? (results[0] as any).rawText ?? '';
            if (value) {
              onScan(value);
              stopScanning();
              return;
            }
          }
        } catch {}
        rafId = requestAnimationFrame(scan);
      };
      rafId = requestAnimationFrame(scan);
    } catch (err) {
      scanning = false;
      error = 'Failed to start camera with basic settings: ' + (err as Error).message;
      toast.error('Unable to access camera. Please check your device settings and try again.');
      onError(error);
    }
  }

  async function toggleTorch() {
    if (!torchSupported) return;
    try {
      if (engine === 'native' && stream) {
        const track = stream.getVideoTracks()[0];
        await track.applyConstraints({
          advanced: [{ torch: !torchEnabled } as any]
        });
        torchEnabled = !torchEnabled;
      } else if (engine === 'zxing' && zxingControls && (zxingControls as any).switchTorch) {
        await (zxingControls as any).switchTorch(!torchEnabled);
        torchEnabled = !torchEnabled;
      }
    } catch (err) {
      console.warn('Failed to toggle torch:', err);
    }
  }

  async function switchCamera() {
    if (cameras.length <= 1) return;
    stopScanning();
    currentCameraIndex = (currentCameraIndex + 1) % cameras.length;
    if (isActive) await startScanning();
  }

  function handleClose() {
    stopScanning();
    onClose();
  }

  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  async function requestCameraPermission() {
    try {
      isInitializing = true;
      error = '';
      const testStream = await navigator.mediaDevices.getUserMedia({ video: true });
      testStream.getTracks().forEach((t) => t.stop());
      const devices = await navigator.mediaDevices.enumerateDevices();
      cameras = devices.filter((d) => d.kind === 'videoinput');
      hasCamera = cameras.length > 0;
      if (hasCamera) {
        toast.success('Camera access granted! You can now scan barcodes.');
        if (isActive) await startScanning();
      } else {
        error = 'No camera found on this device';
        toast.error('No camera found. Please ensure your device has a camera.');
      }
    } catch (err) {
      const errorMessage = (err as Error).message;
      error = 'Failed to access camera: ' + errorMessage;
      if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
        toast.error('Camera permission denied. Please check your browser settings to enable camera access for this site.');
      } else {
        toast.error('Failed to access camera. Please try again or check your device settings.');
      }
    } finally {
      isInitializing = false;
    }
  }

  // React to isActive changes
  $effect(() => {
    if (isActive && hasCamera && !scanning) {
      startScanning();
    } else if (!isActive && scanning) {
      stopScanning();
    }
  });
</script>

<div class="fixed inset-0 z-50 bg-black">
  <!-- Headers and controls preserved -->
  <div class="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/90 to-transparent">
    <!-- Mobile Header -->
    <div class="flex items-center justify-between text-white p-4 md:hidden">
      <div class="flex items-center gap-3">
        <button
          type="button"
          class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
          onclick={handleClose}
          aria-label="Close scanner"
        >
          <IconClose class="h-6 w-6" />
        </button>
        <div>
          <h2 class="text-lg font-semibold">Scan Barcode</h2>
          <p class="text-xs text-white/70">Tap to close</p>
        </div>
      </div>
      <div class="flex items-center gap-2">
        {#if cameras.length > 1}
          <button
            type="button"
            class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
            onclick={switchCamera}
            aria-label="Switch camera"
          >
            <IconCamera class="h-5 w-5" />
          </button>
        {/if}
        {#if torchSupported}
          <button
            type="button"
            class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors {torchEnabled ? 'bg-white/30' : ''}"
            onclick={toggleTorch}
            aria-label="Toggle flashlight"
          >
            <IconFlashlight class="h-5 w-5" />
          </button>
        {/if}
      </div>
    </div>

    <!-- Desktop Header -->
    <div class="hidden md:flex items-center justify-between text-white p-6">
      <h2 class="text-xl font-semibold">Scan Barcode</h2>
      <div class="flex items-center gap-3">
        {#if cameras.length > 1}
          <button
            type="button"
            class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
            onclick={switchCamera}
            aria-label="Switch camera"
          >
            <IconCamera class="h-6 w-6" />
          </button>
        {/if}
        {#if torchSupported}
          <button
            type="button"
            class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors {torchEnabled ? 'bg-white/30' : ''}"
            onclick={toggleTorch}
            aria-label="Toggle flashlight"
          >
            <IconFlashlight class="h-6 w-6" />
          </button>
        {/if}
        <button
          type="button"
          class="btn btn-circle btn-ghost text-white hover:bg-white/20 transition-colors"
          onclick={handleClose}
          aria-label="Close scanner"
        >
          <IconClose class="h-6 w-6" />
        </button>
      </div>
    </div>
  </div>

  <!-- Video Container -->
  <div class="relative h-full w-full">
    {#if hasCamera}
      <video bind:this={videoElement} class="h-full w-full object-cover" muted playsinline></video>

      <!-- Overlay -->
      <div class="absolute inset-0 flex items-center justify-center">
        <div class="relative">
          <div class="h-56 w-56 md:h-64 md:w-64 border-2 border-white/50 bg-transparent">
            <div class="absolute -top-1 -left-1 h-10 w-10 md:h-8 md:w-8 border-l-4 border-t-4 border-primary"></div>
            <div class="absolute -top-1 -right-1 h-10 w-10 md:h-8 md:w-8 border-r-4 border-t-4 border-primary"></div>
            <div class="absolute -bottom-1 -left-1 h-10 w-10 md:h-8 md:w-8 border-l-4 border-b-4 border-primary"></div>
            <div class="absolute -bottom-1 -right-1 h-10 w-10 md:h-8 md:w-8 border-r-4 border-b-4 border-primary"></div>
          </div>

          {#if scanning}
            <div class="absolute inset-0 overflow-hidden">
              <div class="scanning-line absolute left-0 right-0 h-1 md:h-0.5 bg-primary shadow-lg shadow-primary/50"></div>
            </div>
          {/if}

          <div class="absolute -bottom-16 left-1/2 transform -translate-x-1/2 text-center md:hidden">
            <p class="text-white text-sm font-medium">Position barcode in frame</p>
            <p class="text-white/70 text-xs mt-1">Auto-scan when detected</p>
          </div>
        </div>
      </div>
    {:else}
      <div class="flex h-full items-center justify-center text-white">
        <div class="text-center">
          <IconCamera class="mx-auto mb-4 h-16 w-16 text-white/50" />
          <h3 class="mb-2 text-xl font-semibold">Camera Not Available</h3>
          <p class="text-white/70">Please ensure your device has a camera and grant permission to use it.</p>
        </div>
      </div>
    {/if}
  </div>

  <!-- Instructions Bottom Panel -->
  <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent text-center text-white">
    {#if error}
      <div class="p-4 md:p-6">
        <div class="mb-4 rounded-lg bg-error/20 p-4 text-error-content">
          <p class="font-semibold mb-3 text-base">{error}</p>
          {#if error.includes('Permission denied') || error.includes('Camera permission')}
            <div class="text-sm space-y-3">
              {#if isMobileDevice()}
                <div class="text-left">
                  <p class="mb-2 font-medium">On mobile devices:</p>
                  <ul class="list-disc list-inside space-y-1 text-xs">
                    <li>Tap "Allow" when prompted for camera access</li>
                    <li>Check your browser settings if you previously denied access</li>
                    <li>Try refreshing the page and allowing camera access</li>
                    <li>Ensure you're using a secure connection (HTTPS)</li>
                  </ul>
                </div>
              {:else}
                <div class="text-left">
                  <p class="mb-2 font-medium">On desktop:</p>
                  <ul class="list-disc list-inside space-y-1 text-xs">
                    <li>Click "Allow" when prompted for camera access</li>
                    <li>Check the camera icon in your browser's address bar</li>
                    <li>Go to browser settings and enable camera for this site</li>
                    <li>Ensure no other applications are using the camera</li>
                  </ul>
                </div>
              {/if}
              <div class="flex flex-col gap-2 mt-4 md:flex-row md:justify-center">
                <button type="button" class="btn btn-sm btn-primary w-full md:w-auto" onclick={requestCameraPermission} disabled={isInitializing}>
                  {isInitializing ? 'Requesting...' : 'Request Camera Access'}
                </button>
                <button type="button" class="btn btn-sm btn-outline w-full md:w-auto" onclick={() => location.reload()}>
                  Refresh Page
                </button>
              </div>
            </div>
          {/if}
        </div>
      </div>
    {:else if isInitializing}
      <div class="p-6">
        <p class="text-lg md:text-xl mb-2">Initializing camera...</p>
        <p class="text-sm text-white/70">
          {isMobileDevice() ? 'Please tap "Allow" when prompted for camera access' : 'Please click "Allow" when prompted for camera access'}
        </p>
      </div>
    {:else if scanning}
      <div class="hidden md:block p-6">
        <p class="text-lg">Position the barcode within the frame</p>
        <p class="text-sm text-white/70">The barcode will be scanned automatically</p>
      </div>
    {:else}
      <div class="p-6">
        <p class="text-lg md:text-xl mb-2">Camera ready</p>
        <p class="text-sm text-white/70">
          {isMobileDevice() ? 'Tap anywhere to start scanning' : 'Click to start scanning'}
        </p>
      </div>
    {/if}
  </div>
</div>

<style>
  .scanning-line {
    animation: scan 2s linear infinite;
  }
  @keyframes scan {
    0% { top: 0; }
    100% { top: 100%; }
  }
</style>

