/**
 * Configuration presets for ImageUpload component
 * Simplifies component usage by providing common configurations
 */


import type { ImageUploadConfig } from '$lib/config';


/**
 * Default configuration
 */
import {
    BUTTON_TEXTS,
    DIMENSIONS,
    ERROR_MESSAGES,
    <PERSON><PERSON><PERSON><PERSON><PERSON>DE<PERSON>,
    VALIDATION
} from '$lib/config';

export const defaultImageUploadConfig: ImageUploadConfig = {
	maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_KB,
	acceptedTypes: [...VALIDATION.ACCEPTED_IMAGE_TYPES],
	showPreview: true,
	showCameraButton: true,
	showFileButton: true,
	placeholder: PLACEHOLDERS.IMAGE_UPLOAD,
	buttonText: BUTTON_TEXTS.CHOOSE_FILE,
	cameraText: BUTTON_TEXTS.TAKE_PHOTO,
	removeText: BUTTON_TEXTS.REMOVE_IMAGE,
	errorMessages: {
		fileSize: ERROR_MESSAGES.FILE_TOO_LARGE,
		fileType: ERROR_MESSAGES.INVALID_FILE_TYPE,
		upload: ERROR_MESSAGES.UPLOAD_FAILED
	},
	styling: {
		containerClass: 'w-full',
		buttonClass: 'btn btn-outline',
		previewClass: 'rounded-lg max-h-64 w-auto object-contain mx-auto'
	}
};

/**
 * Preset configurations for common use cases
 */
export const imageUploadPresets = {
	/**
	 * Profile picture upload - smaller size, square preview
	 */
	profile: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_COMPACT_KB,
		placeholder: 'Upload profile picture',
		styling: {
			...defaultImageUploadConfig.styling,
			previewClass: 'rounded-full w-32 h-32 object-cover mx-auto'
		}
	} as ImageUploadConfig,

	/**
	 * Product/inventory image - larger size, product-focused
	 */
	product: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_GALLERY_KB,
		placeholder: 'Upload product image',
		buttonText: 'Add Product Photo',
		cameraText: 'Scan Product'
	} as ImageUploadConfig,

	/**
	 * Client photo - medium size, professional styling
	 */
	client: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_GALLERY_KB,
		placeholder: 'Upload client photo',
		buttonText: 'Add Photo',
		styling: {
			...defaultImageUploadConfig.styling,
			previewClass: 'rounded-lg w-48 h-48 object-cover mx-auto'
		}
	} as ImageUploadConfig,

	/**
	 * Document/receipt upload - larger file size, different types
	 */
	document: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_KB * 2,
		acceptedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/pdf'],
		placeholder: 'Upload document or receipt',
		buttonText: 'Choose Document',
		cameraText: 'Scan Document',
		errorMessages: {
			...defaultImageUploadConfig.errorMessages,
			fileType: 'Please select a valid image or PDF file'
		}
	} as ImageUploadConfig,

	/**
	 * Compact upload - minimal UI, smaller buttons
	 */
	compact: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_COMPACT_KB,
		showPreview: false,
		placeholder: PLACEHOLDERS.IMAGE_UPLOAD_COMPACT,
		buttonText: BUTTON_TEXTS.UPLOAD,
		cameraText: BUTTON_TEXTS.USE_CAMERA,
		styling: {
			...defaultImageUploadConfig.styling,
			buttonClass: 'btn btn-sm btn-outline',
			containerClass: 'w-auto'
		}
	} as ImageUploadConfig,

	/**
	 * Gallery upload - multiple images, larger previews
	 */
	gallery: {
		...defaultImageUploadConfig,
		maxSizeKB: DIMENSIONS.MAX_IMAGE_SIZE_GALLERY_KB,
		placeholder: 'Add to gallery',
		buttonText: 'Add Images',
		styling: {
			...defaultImageUploadConfig.styling,
			previewClass: 'rounded-lg max-h-96 w-auto object-contain mx-auto'
		}
	} as ImageUploadConfig
};

/**
 * Helper function to merge custom config with preset
 */
export function createImageUploadConfig(
	preset: keyof typeof imageUploadPresets | 'default' = 'default',
	customConfig?: Partial<ImageUploadConfig>
): ImageUploadConfig {
	const baseConfig = preset === 'default'
		? defaultImageUploadConfig
		: imageUploadPresets[preset];

	if (!customConfig) {
		return baseConfig;
	}

	return {
		...baseConfig,
		...customConfig,
		errorMessages: {
			...baseConfig.errorMessages,
			...customConfig.errorMessages
		},
		styling: {
			...baseConfig.styling,
			...customConfig.styling
		}
	};
}

/**
 * Validation helper
 */
export function validateImageUpload(
	file: File,
	config: ImageUploadConfig
): { isValid: boolean; error?: string } {
	// Check file size
	if (config.maxSizeKB && file.size > config.maxSizeKB * 1024) {
		return {
			isValid: false,
			error: config.errorMessages?.fileSize || `File size must be less than ${config.maxSizeKB}KB`
		};
	}

	// Check file type
	if (config.acceptedTypes && !config.acceptedTypes.includes(file.type)) {
		return {
			isValid: false,
			error: config.errorMessages?.fileType || 'Invalid file type'
		};
	}

	return { isValid: true };
}

/**
 * File size formatter
 */
export function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 Bytes';

	const k = 1024;
	const sizes = ['Bytes', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
