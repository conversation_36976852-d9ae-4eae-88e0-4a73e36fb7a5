# Configuration Consolidation Migration Guide

This guide outlines the comprehensive consolidation of configurations, magic numbers, type definitions, and reusable constants in the Hairloom CRM codebase.

## 📁 New Structure

```
src/lib/
├── config/
│   ├── index.ts          # Main export file with utilities
│   ├── app.ts            # App-wide configuration constants
│   └── constants.ts      # Shared constants and strings
├── types/
│   └── index.ts          # Unified type definitions
└── styles/
    └── utilities.css     # DaisyUI utility classes
```

## 🔧 What Was Consolidated

### 1. Configuration Constants (`src/lib/config/app.ts`)

- **Timing constants**: Toast durations, animation speeds, camera timeouts
- **Dimensions**: Image sizes, camera dimensions, pagination limits
- **Currency**: Conversion rates, formatting settings
- **Validation**: Field limits, password requirements, file types
- **Feature flags**: Enable/disable features across the app
- **Storage keys**: LocalStorage and SessionStorage keys
- **API configuration**: Endpoints, timeouts, retry settings
- **Route paths**: Centralized route definitions

### 2. Shared Constants (`src/lib/config/constants.ts`)

- **Error messages**: Standardized error text
- **Success messages**: Consistent success feedback
- **Info messages**: Loading states, warnings, placeholders
- **Confirmation dialogs**: Reusable confirmation configurations
- **Button texts**: Standardized button labels
- **Keyboard shortcuts**: App-wide keyboard shortcuts
- **Regex patterns**: Validation patterns and safe error patterns

### 3. Unified Types (`src/lib/types/index.ts`)

- **Domain types**: InventoryItem, Client, ShoppingListItem, Template, User
- **Form data types**: Consistent form interfaces
- **API response types**: Standardized response formats
- **Component prop types**: Reusable component interfaces
- **Utility types**: Helper types for common patterns

### 4. DaisyUI Utilities (`src/lib/styles/utilities.css`)

- **Button utilities**: Enhanced button styles with consistent hover effects
- **Card utilities**: Refined card styles with interactive variants
- **Input utilities**: Enhanced form inputs with focus styling
- **Navigation utilities**: Consistent nav item styling with color variants
- **Modal utilities**: Standardized modal positioning and styling
- **Status utilities**: Consistent status indicators and badges
- **Layout utilities**: Responsive grid and flex utilities
- **Animation utilities**: Fade, stagger, and hover animations

## 🚀 How to Use

### Import Everything from One Place

```typescript
import {
  TIMING,
  DIMENSIONS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  ROUTES,
  formatCurrency,
  isValidEmail,
  getQuantityStatus,
  type InventoryItem,
  type ApiResponse
} from '$lib/config';
```

### Replace Magic Numbers

```typescript
// ❌ Before
setTimeout(() => {
  window.location.href = '/app/inventory';
}, 1000);

// ✅ After
setTimeout(() => {
  window.location.href = ROUTES.INVENTORY;
}, TIMING.REDIRECT_DELAY);
```

### Use Centralized Error Messages

```typescript
// ❌ Before
toast.error('Camera access denied. Please allow camera permissions and try again.');

// ✅ After
toast.error(ERROR_MESSAGES.CAMERA_ACCESS_DENIED);
```

### Use Utility Functions

```typescript
// ❌ Before
const formatted = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD'
}).format(priceInCents / 100);

// ✅ After
const formatted = formatCurrency(priceInCents);
```

### Use Validation Helpers

```typescript
// ❌ Before
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
if (!emailRegex.test(email)) {
  // handle error
}

// ✅ After
if (!isValidEmail(email)) {
  toast.error(ERROR_MESSAGES.INVALID_EMAIL);
}
```

### Use DaisyUI Utility Classes

```svelte
<!-- ❌ Before -->
<button class="btn btn-primary transition-all duration-200 hover:shadow-md"> Save </button>

<!-- ✅ After -->
<button class="btn-primary-enhanced">
  {BUTTON_TEXTS.SAVE}
</button>
```

### Use Consistent Types

```typescript
// ❌ Before
interface Item {
  id: string;
  name: string;
  quantity: number;
  // ... other fields
}

// ✅ After
import type { InventoryItem } from '$lib/config';
// Use InventoryItem directly
```

## 📋 Migration Checklist

### Phase 1: Update Imports

- [ ] Replace scattered imports with centralized config imports
- [ ] Update type imports to use unified types
- [ ] Add utility CSS import to components using repeated classes

### Phase 2: Replace Magic Numbers

- [ ] Replace hardcoded timeouts with `TIMING` constants
- [ ] Replace hardcoded dimensions with `DIMENSIONS` constants
- [ ] Replace hardcoded validation limits with `VALIDATION` constants

### Phase 3: Standardize Messages

- [ ] Replace hardcoded error messages with `ERROR_MESSAGES`
- [ ] Replace success messages with `SUCCESS_MESSAGES`
- [ ] Replace button texts with `BUTTON_TEXTS`
- [ ] Replace placeholders with `PLACEHOLDERS`

### Phase 4: Update Styling

- [ ] Replace repeated DaisyUI class combinations with utility classes
- [ ] Update button styling to use enhanced variants
- [ ] Update card styling to use refined variants
- [ ] Update form styling to use enhanced inputs

### Phase 5: Type Safety

- [ ] Replace `any` types with proper interfaces
- [ ] Update API response handling to use `ApiResponse<T>`
- [ ] Ensure form data uses proper form interfaces
- [ ] Update component props to use standardized prop types

## 🎯 Benefits

1. **Maintainability**: Single source of truth for all constants
2. **Consistency**: Standardized styling and messaging across the app
3. **Type Safety**: Proper TypeScript interfaces eliminate runtime errors
4. **Developer Experience**: Auto-completion and IntelliSense for all constants
5. **Performance**: Reduced bundle size through deduplication
6. **Accessibility**: Consistent touch targets and responsive design
7. **Theming**: Centralized color usage following DaisyUI standards

## 🔄 Gradual Migration

You can migrate components gradually:

1. Start with new components using the centralized config
2. Update existing components when making changes
3. Use the utility functions and validation helpers immediately
4. Apply utility classes when refactoring styling
5. Update types when adding new features

## 📚 Examples

### Complete Component Migration Example

```svelte
<!-- Before -->
<script lang="ts">
  import { toast } from '$lib/ui/toast';

  let isLoading = false;

  async function handleSubmit() {
    isLoading = true;
    try {
      // API call
      toast.success('Item saved successfully');
      setTimeout(() => {
        window.location.href = '/app/inventory';
      }, 1000);
    } catch (error) {
      toast.error('Failed to save item');
    } finally {
      isLoading = false;
    }
  }
</script>

<button
  class="btn btn-primary transition-all duration-200 hover:shadow-md"
  onclick={handleSubmit}
  disabled={isLoading}
>
  {#if isLoading}
    <span class="loading loading-spinner loading-sm"></span>
  {/if}
  Save
</button>
```

```svelte
<!-- After -->
<script lang="ts">
  import { toast } from '$lib/ui/toast';
  import { TIMING, ROUTES, SUCCESS_MESSAGES, ERROR_MESSAGES, BUTTON_TEXTS } from '$lib/config';

  let isLoading = false;

  async function handleSubmit() {
    isLoading = true;
    try {
      // API call
      toast.success(SUCCESS_MESSAGES.INVENTORY_ITEM_CREATED);
      setTimeout(() => {
        window.location.href = ROUTES.INVENTORY;
      }, TIMING.REDIRECT_DELAY);
    } catch (error) {
      toast.error(ERROR_MESSAGES.GENERIC_ERROR);
    } finally {
      isLoading = false;
    }
  }
</script>

<button class="btn-primary-enhanced" onclick={handleSubmit} disabled={isLoading}>
  {#if isLoading}
    <span class="loading-spinner-enhanced"></span>
  {/if}
  {BUTTON_TEXTS.SAVE}
</button>
```

This consolidation provides a solid foundation for maintainable, consistent, and type-safe code across the entire Hairloom CRM application.
