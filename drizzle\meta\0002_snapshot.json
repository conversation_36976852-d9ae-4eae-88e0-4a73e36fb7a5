{"id": "62c533ed-293e-457e-bab8-0b5179eac9c7", "prevId": "3b64ba5c-1362-48ba-9471-f1e2731d7148", "version": "7", "dialect": "postgresql", "tables": {"public.client_notes": {"name": "client_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "template_id": {"name": "template_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "note_data": {"name": "note_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"client_notes_client_id_clients_id_fk": {"name": "client_notes_client_id_clients_id_fk", "tableFrom": "client_notes", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "client_notes_template_id_templates_id_fk": {"name": "client_notes_template_id_templates_id_fk", "tableFrom": "client_notes", "tableTo": "templates", "columnsFrom": ["template_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clients_user_id_users_id_fk": {"name": "clients_user_id_users_id_fk", "tableFrom": "clients", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_verification_tokens": {"name": "email_verification_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "hashed_otp_code": {"name": "hashed_otp_code", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_verification_tokens_user_id_users_id_fk": {"name": "email_verification_tokens_user_id_users_id_fk", "tableFrom": "email_verification_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.inventory_items": {"name": "inventory_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "hashed_otp_code": {"name": "hashed_otp_code", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"password_reset_tokens_user_id_users_id_fk": {"name": "password_reset_tokens_user_id_users_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.templates": {"name": "templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "template_definition": {"name": "template_definition", "type": "jsonb", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "template_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"templates_user_id_users_id_fk": {"name": "templates_user_id_users_id_fk", "tableFrom": "templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_account_recovery_codes": {"name": "user_account_recovery_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "hashed_code": {"name": "hashed_code", "type": "text", "primaryKey": false, "notNull": true}, "used_at": {"name": "used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_account_recovery_codes_user_id_users_id_fk": {"name": "user_account_recovery_codes_user_id_users_id_fk", "tableFrom": "user_account_recovery_codes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_oauth_accounts": {"name": "user_oauth_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "provider_user_id": {"name": "provider_user_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_oauth_accounts_user_id_users_id_fk": {"name": "user_oauth_accounts_user_id_users_id_fk", "tableFrom": "user_oauth_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "hashed_password": {"name": "hashed_password", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "profile_photo_url": {"name": "profile_photo_url", "type": "text", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webauthn_challenges": {"name": "webauthn_challenges", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"webauthn_challenges_user_id_users_id_fk": {"name": "webauthn_challenges_user_id_users_id_fk", "tableFrom": "webauthn_challenges", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webauthn_credentials": {"name": "webauthn_credentials", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "credential_id": {"name": "credential_id", "type": "text", "primaryKey": false, "notNull": true}, "public_key": {"name": "public_key", "type": "text", "primaryKey": false, "notNull": true}, "counter": {"name": "counter", "type": "text", "primaryKey": false, "notNull": true}, "transports": {"name": "transports", "type": "text", "primaryKey": false, "notNull": false}, "device_type": {"name": "device_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "backed_up": {"name": "backed_up", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "last_used_at": {"name": "last_used_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"webauthn_credentials_user_id_users_id_fk": {"name": "webauthn_credentials_user_id_users_id_fk", "tableFrom": "webauthn_credentials", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"webauthn_credentials_credential_id_unique": {"name": "webauthn_credentials_credential_id_unique", "nullsNotDistinct": false, "columns": ["credential_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.product_action_type": {"name": "product_action_type", "schema": "public", "values": ["Used", "Sold"]}, "public.template_status": {"name": "template_status", "schema": "public", "values": ["draft", "active", "archived"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}