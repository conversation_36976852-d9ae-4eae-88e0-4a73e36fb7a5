import { describe, expect, it } from 'vitest';
import { z } from 'zod';
import { createRuneForm } from './index.svelte';

// A tiny schema representative of typical usage
const schema = z.object({
  id: z.uuid(),
  name: z.string().min(1),
  quantity: z.coerce.number().int().nonnegative(),
});

describe('createRuneForm (zod v4)', () => {
  it('validates successfully with correct values', () => {
    const form = createRuneForm(schema, {
      id: '00000000-0000-0000-0000-000000000000',
      name: 'Scissors',
      quantity: 3,
    });

    expect(form.validate()).toBe(true);
    expect(form.errors).toEqual({});
  });

  it('collects field errors for invalid values', () => {
    const form = createRuneForm(schema, {
      id: 'not-a-uuid',
      name: '',
      quantity: -5,
    } as any);

    expect(form.validate()).toBe(false);
    expect(form.errors.id?.length).toBeGreaterThan(0);
    expect(form.errors.name?.length).toBeGreaterThan(0);
    expect(form.errors.quantity?.length).toBeGreaterThan(0);
  });
});

