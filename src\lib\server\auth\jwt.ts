import { getJWTConfig } from '$lib/server/config/secrets';
import type { RequestEvent } from '@sveltejs/kit';
import { SignJWT, decodeJwt, jwtVerify, type JWTPayload } from 'jose';

// Token lifetimes
export const ACCESS_TOKEN_TTL_SECONDS = 60 * 15; // 15 minutes
export const REFRESH_TOKEN_TTL_SECONDS = 60 * 60 * 24 * 30; // 30 days
export const REAUTH_TOKEN_TTL_SECONDS = 60 * 15; // 15 minutes window for sensitive ops

export const ACCESS_TOKEN_COOKIE = 'access_token';
export const REFRESH_TOKEN_COOKIE = 'refresh_token';
export const REAUTH_TOKEN_COOKIE = 'reauth_token';

export type AccessTokenClaims = JWTPayload & {
  sub: string; // user id
  sid?: string; // session id (optional)
  typ: 'access';
};

export type RefreshTokenClaims = JWTPayload & {
  sub: string;
  sid: string; // tie refresh token to a DB session for invalidation
  typ: 'refresh';
  ver: number; // rotation version
};

export type ReauthTokenClaims = JWTPayload & {
  sub: string;
  sid: string;
  typ: 'reauth';
};

function getAccessKey(): Uint8Array {
  const { JWT_ACCESS_SECRET } = getJWTConfig();
  return new TextEncoder().encode(JWT_ACCESS_SECRET);
}

function getRefreshKey(): Uint8Array {
  const { JWT_REFRESH_SECRET } = getJWTConfig();
  return new TextEncoder().encode(JWT_REFRESH_SECRET);
}

export async function generateAccessToken(userId: string, sessionId?: string, extra?: JWTPayload) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const now = Math.floor(Date.now() / 1000);
  return await new SignJWT({ ...extra, sub: userId, sid: sessionId, typ: 'access' } as AccessTokenClaims)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt(now)
    .setIssuer(JWT_ISSUER)
    .setAudience(JWT_AUDIENCE)
    .setExpirationTime(now + ACCESS_TOKEN_TTL_SECONDS)
    .sign(getAccessKey());
}

export async function generateRefreshToken(userId: string, sessionId: string, version = 1, extra?: JWTPayload) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const now = Math.floor(Date.now() / 1000);
  return await new SignJWT({ ...extra, sub: userId, sid: sessionId, typ: 'refresh', ver: version } as RefreshTokenClaims)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt(now)
    .setIssuer(JWT_ISSUER)
    .setAudience(JWT_AUDIENCE)
    .setExpirationTime(now + REFRESH_TOKEN_TTL_SECONDS)
    .sign(getRefreshKey());
}

export async function generateReauthToken(userId: string, sessionId: string, extra?: JWTPayload) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const now = Math.floor(Date.now() / 1000);
  return await new SignJWT({ ...extra, sub: userId, sid: sessionId, typ: 'reauth' } as ReauthTokenClaims)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt(now)
    .setIssuer(JWT_ISSUER)
    .setAudience(JWT_AUDIENCE)
    .setExpirationTime(now + REAUTH_TOKEN_TTL_SECONDS)
    .sign(getAccessKey());
}

export async function verifyAccessToken(token: string) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const { payload } = await jwtVerify(token, getAccessKey(), {
    algorithms: ['HS256'],
    issuer: JWT_ISSUER,
    audience: JWT_AUDIENCE
  });
  return payload as AccessTokenClaims;
}

export async function verifyRefreshToken(token: string) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const { payload } = await jwtVerify(token, getRefreshKey(), {
    algorithms: ['HS256'],
    issuer: JWT_ISSUER,
    audience: JWT_AUDIENCE
  });
  return payload as RefreshTokenClaims;
}

export async function verifyReauthToken(token: string) {
  const { JWT_ISSUER, JWT_AUDIENCE } = getJWTConfig();
  const { payload } = await jwtVerify(token, getAccessKey(), {
    algorithms: ['HS256'],
    issuer: JWT_ISSUER,
    audience: JWT_AUDIENCE
  });
  return payload as ReauthTokenClaims;
}

export function decodeToken(token: string) {
  return decodeJwt(token);
}

export function isTokenExpired(exp: number | undefined) {
  if (!exp) return true;
  const now = Math.floor(Date.now() / 1000);
  return exp <= now;
}

export async function issueSessionJWTs(event: RequestEvent, userId: string, sessionId: string) {
  const access = await generateAccessToken(userId, sessionId);
  const refresh = await generateRefreshToken(userId, sessionId);
  setJWTCookies(event, access, refresh);
}

export function setJWTCookies(event: RequestEvent, accessToken: string, refreshToken: string) {
  // Access token httpOnly cookie, short-lived
  event.cookies.set(ACCESS_TOKEN_COOKIE, accessToken, {
    httpOnly: true,
    secure: process.env.VERCEL_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: ACCESS_TOKEN_TTL_SECONDS,
  });
  // Refresh token httpOnly cookie, long-lived
  event.cookies.set(REFRESH_TOKEN_COOKIE, refreshToken, {
    httpOnly: true,
    secure: process.env.VERCEL_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: REFRESH_TOKEN_TTL_SECONDS,
  });
}

export function setReauthCookie(event: RequestEvent, token: string) {
  event.cookies.set(REAUTH_TOKEN_COOKIE, token, {
    httpOnly: true,
    secure: process.env.VERCEL_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: REAUTH_TOKEN_TTL_SECONDS,
  });
}

export function clearJWTCookies(event: RequestEvent) {
  event.cookies.set(ACCESS_TOKEN_COOKIE, '', { httpOnly: true, path: '/', maxAge: 0, sameSite: 'lax', secure: process.env.VERCEL_ENV === 'production' });
  event.cookies.set(REFRESH_TOKEN_COOKIE, '', { httpOnly: true, path: '/', maxAge: 0, sameSite: 'lax', secure: process.env.VERCEL_ENV === 'production' });
  event.cookies.set(REAUTH_TOKEN_COOKIE, '', { httpOnly: true, path: '/', maxAge: 0, sameSite: 'lax', secure: process.env.VERCEL_ENV === 'production' });
}

export async function hasRecentReauth(event: RequestEvent): Promise<boolean> {
  const token = event.cookies.get(REAUTH_TOKEN_COOKIE);
  if (!token) return false;
  try {
    await verifyReauthToken(token);
    return true;
  } catch {
    return false;
  }
}

