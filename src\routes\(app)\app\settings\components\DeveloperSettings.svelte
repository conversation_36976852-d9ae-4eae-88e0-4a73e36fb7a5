<!--
	@component
	Developer Settings Module - Handles developer tools and testing features
-->
<script lang="ts">
	import { TIMING } from '$lib/config';
	import { confirm } from '$lib/ui/confirm';
	import { toast } from '$lib/ui/toast';
	import IconCode from '~icons/icon-park-outline/code';

	async function handleReseed() {
		const confirmed = await confirm({
			title: 'Reset and Reseed Database?',
			message:
				'This is a destructive action that will wipe the entire database and replace it with development test data. Are you sure you want to proceed?',
			confirmText: 'Yes, Reseed Database',
			cancelText: 'Cancel'
		});

		if (confirmed) {
			const reseedPromise = fetch('/api/db/reseed', {
				method: 'POST'
			}).then((res) => {
				if (!res.ok) {
					return res.json().then((err) => Promise.reject(err));
				}
				return res.json();
			});

			toast.custom(reseedPromise, {
				loading: 'Reseeding database...',
				success: 'Database reseeded successfully!',
				error: (err: unknown) => {
					if (typeof err === 'object' && err !== null && 'message' in err) {
						return `Reseeding failed: ${err.message || 'Unknown error'}`;
					}
					return 'Reseeding failed: An unknown error occurred.';
				}
			});
		}
	}

		let authState: any = $state();
		async function refreshAuthState() {
			try {
				const res = await fetch('/api/dev/auth-state');
				if (!res.ok) return toast.error('Auth state endpoint not available.');
				authState = await res.json();
			} catch (e) {
				toast.error('Failed to load auth state');
			}
		}

</script>

<div class="space-y-6">
	<!-- Database Management -->
	<div class="collapse collapse-arrow bg-base-200/50">
		<input type="checkbox" />
		<div class="collapse-title text-xl font-medium">
			<IconCode class="mr-2 h-5 w-5 inline" />
			Developer Actions
		</div>
		<div class="collapse-content">
			<p class="text-base-content/70 mb-4">
				These actions are for development and testing purposes only.
			</p>
			<div class="card-actions justify-end">
				<button class="btn btn-warning" onclick={handleReseed}> Reseed Database </button>
			</div>
		</div>
	</div>

	<!-- Test Notifications -->
	<div class="collapse collapse-arrow bg-base-200/50">
		<input type="checkbox" />
		<div class="collapse-title text-xl font-medium">
			<IconCode class="mr-2 h-5 w-5 inline" />
			Test Toast Notifications
		</div>
		<div class="collapse-content">
			<p class="text-base-content/70 mb-4">
				Click the buttons below to test the different types of toast notifications.
			</p>
			<div class="card-actions flex-wrap justify-center gap-2">
				<button
					class="btn btn-success"
					onclick={() => toast.success('This is a success toast.')}>Success</button
				>
				<button class="btn btn-error" onclick={() => toast.error('This is an error toast.')}
					>Error</button
				>
				<button class="btn btn-info" onclick={() => toast.info('This is an info toast.')}
					>Info</button
				>
				<button
					class="btn btn-warning"
					onclick={() => toast.warning('This is a warning toast.')}>Warning</button
				>
				<button
					class="btn btn-primary"
					onclick={() => {
						const promise = new Promise((_, reject) =>
							setTimeout(() => reject(new Error('Test error')), TIMING.TOAST_ERROR_DURATION)
						);
						toast.custom(promise, {
							loading: 'Simulating a long task (4s)...',
							success: 'Task complete!',
							error: (err: unknown) =>
								`Task failed: ${err instanceof Error ? err.message : 'An unknown error occurred.'}`
						});
					}}>Loading (Promise)</button
				>
			</div>
		</div>
	</div>

	<!-- Auth State Visualization (Dev only) -->
	<div class="collapse collapse-arrow bg-base-200/50">
		<input type="checkbox" />
		<div class="collapse-title text-xl font-medium">
			<IconCode class="mr-2 h-5 w-5 inline" />
			Auth State
		</div>
		<div class="collapse-content space-y-4">
			<p class="text-base-content/70">Inspect auth cookies and token expirations. Dev environments only.</p>
			<div class="flex gap-2">
				<button class="btn btn-outline btn-sm" onclick={refreshAuthState}>Refresh</button>
			</div>
			{#if authState}
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div class="card bg-base-100 border border-base-300/40">
						<div class="card-body">
							<h3 class="card-title">User</h3>
							<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.user, null, 2)}</pre>
						</div>
					</div>
					<div class="card bg-base-100 border border-base-300/40">
						<div class="card-body">
							<h3 class="card-title">Session</h3>
							<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.session, null, 2)}</pre>
						</div>
					</div>
					<div class="card bg-base-100 border border-base-300/40 md:col-span-2">
						<div class="card-body">
							<h3 class="card-title">Cookies</h3>
							<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.cookies, null, 2)}</pre>
						</div>
					</div>
					<div class="card bg-base-100 border border-base-300/40 md:col-span-2">
						<div class="card-body">
							<h3 class="card-title">Tokens</h3>
							<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.tokens, null, 2)}</pre>
						</div>
					</div>
				</div>
			{:else}
				<p class="text-sm text-base-content/60">Click Refresh to load.</p>
			{/if}
		</div>
	</div>
</div>

		<!-- Auth State Visualization (Dev only) -->
		<div class="collapse collapse-arrow bg-base-200/50">
			<input type="checkbox" />
			<div class="collapse-title text-xl font-medium">
				<IconCode class="mr-2 h-5 w-5 inline" />
				Auth State
			</div>
			<div class="collapse-content space-y-4">
				<p class="text-base-content/70">Inspect auth cookies and token expirations. Dev environments only.</p>
				<div class="flex gap-2">
					<button class="btn btn-outline btn-sm" onclick={refreshAuthState}>Refresh</button>
				</div>
				{#if authState}
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div class="card bg-base-100 border border-base-300/40">
							<div class="card-body">
								<h3 class="card-title">User</h3>
								<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.user, null, 2)}</pre>
							</div>
						</div>
						<div class="card bg-base-100 border border-base-300/40">
							<div class="card-body">
								<h3 class="card-title">Session</h3>
								<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.session, null, 2)}</pre>
							</div>
						</div>
						<div class="card bg-base-100 border border-base-300/40 md:col-span-2">
							<div class="card-body">
								<h3 class="card-title">Cookies</h3>
								<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.cookies, null, 2)}</pre>
							</div>
						</div>
						<div class="card bg-base-100 border border-base-300/40 md:col-span-2">
							<div class="card-body">
								<h3 class="card-title">Tokens</h3>
								<pre class="text-xs opacity-80 overflow-auto">{JSON.stringify(authState.tokens, null, 2)}</pre>
							</div>
						</div>
					</div>
				{:else}
					<p class="text-sm text-base-content/60">Click Refresh to load.</p>
				{/if}
			</div>
		</div>

						);
						toast.custom(promise, {
							loading: 'Simulating a long task (4s)...',
							success: 'Task complete!',
							error: (err: unknown) =>
								`Task failed: ${err instanceof Error ? err.message : 'An unknown error occurred.'}`
						});
					}}>Loading (Promise)</button
				>
			</div>
		</div>
	</div>
</div>
