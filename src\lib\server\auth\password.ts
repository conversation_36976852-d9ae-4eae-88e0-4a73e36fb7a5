import { hash, verify } from '@node-rs/argon2';
import { createHash } from 'crypto';

export async function hashPassword(password: string): Promise<string> {
	return await hash(password, {
		memoryCost: 19456,
		timeCost: 2,
		outputLen: 32,
		parallelism: 1
	});
}

export async function verifyPasswordHash(hash: string, password: string): Promise<boolean> {
	return await verify(hash, password);
}

export async function verifyPasswordStrength(password: string): Promise<boolean> {
	if (password.length < 8 || password.length > 255) {
		return false;
	}
	try {
		// HIBP Pwned Passwords API v3 requires the password to be hashed with SHA-1.
		const hash = createHash('sha1').update(password).digest('hex').toUpperCase();
		const hashPrefix = hash.slice(0, 5);

		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), 5000); // 5-second timeout

		const response = await fetch(`https://api.pwnedpasswords.com/range/${hashPrefix}`, {
			signal: controller.signal
		});
		clearTimeout(timeoutId);

		if (!response.ok) {
			console.error('HIBP API request failed:', response.status, response.statusText);
			return false; // Treat API errors as a failed strength check
		}

		const data = await response.text();
		const items = data.split('\r\n'); // API uses CRLF line endings
		for (const item of items) {
			const hashSuffix = item.slice(0, 35); // Suffixes from API are already uppercase
			if (hash === hashPrefix + hashSuffix) {
				return false; // Password found in HIBP database
			}
		}
		return true; // Password not found in HIBP database
	} catch (error) {
		console.error('Error during password strength verification (HIBP check):', error);
		// In case of any error (network, timeout, etc.), err on the side of caution
		// and consider the password check as failed.
		return false;
	}
}
