import { env } from '$env/dynamic/private';
import { google } from '$lib/server/auth/google';
import { error, redirect } from '@sveltejs/kit';
import { generateCodeVerifier, generateState } from 'arctic';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ cookies, url: requestUrl }) => {
	if (!google) {
		throw error(500, 'Google login is not configured.');
	}
	const loginHint = requestUrl.searchParams.get('login_hint');

	const state = generateState();
	const codeVerifier = generateCodeVerifier();
	const url = google.createAuthorizationURL(state, codeVerifier, ['profile', 'email']);
	url.searchParams.set('prompt', 'select_account');
	if (loginHint) {
		url.searchParams.set('login_hint', loginHint);
	}

	cookies.set('google_oauth_state', state, {
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		httpOnly: true,
		maxAge: 60 * 10,
		sameSite: 'lax'
	});

	cookies.set('google_code_verifier', codeVerifier, {
		path: '/',
		secure: env.VERCEL_ENV === 'production',
		httpOnly: true,
		maxAge: 60 * 10,
		sameSite: 'lax'
	});

	return redirect(302, url.toString());
};
