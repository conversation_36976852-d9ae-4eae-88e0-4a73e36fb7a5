import { env } from '$env/dynamic/private';
import { z } from 'zod';

/**
 * Comprehensive Zod validation schemas for all environment variables
 * This ensures fail-fast validation with detailed error messages
 */

// Database configuration
const DatabaseConfigSchema = z.object({
  POSTGRES_URL: z.string().url('POSTGRES_URL must be a valid PostgreSQL connection string'),
});

// Email service configuration
const EmailConfigSchema = z.object({
  RESEND_API_KEY: z.string().min(1, 'RESEND_API_KEY is required for email functionality'),
  EMAIL_FROM: z.string().email('EMAIL_FROM must be a valid email address'),
});

// Security configuration
const SecurityConfigSchema = z.object({
  ENCRYPTION_SECRET: z.string().min(32, 'ENCRYPTION_SECRET must be at least 32 characters for security'),
  TWO_FACTOR_AUTHENTICATION: z.string().min(1, 'TWO_FACTOR_AUTHENTICATION configuration is required'),
});

// OAuth configuration
const OAuthConfigSchema = z.object({
  GOOGLE_CLIENT_ID: z.string().min(1, 'GOOGLE_CLIENT_ID is required for Google OAuth'),
  GOOGLE_CLIENT_SECRET: z.string().min(1, 'GOOGLE_CLIENT_SECRET is required for Google OAuth'),
  GOOGLE_REDIRECT_URI: z.string().url('GOOGLE_REDIRECT_URI must be a valid URL').optional(),
  GITHUB_CLIENT_ID: z.string().min(1, 'GITHUB_CLIENT_ID is required for GitHub OAuth').optional(),
  GITHUB_CLIENT_SECRET: z.string().min(1, 'GITHUB_CLIENT_SECRET is required for GitHub OAuth').optional(),
  GITHUB_REDIRECT_URI: z.string().url('GITHUB_REDIRECT_URI must be a valid URL').optional(),
});

// JWT configuration
const JWTConfigSchema = z.object({
  JWT_ACCESS_SECRET: z.string().min(32, 'JWT_ACCESS_SECRET must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT_REFRESH_SECRET must be at least 32 characters'),
  JWT_ISSUER: z.string().min(1).default('hairloom'),
  JWT_AUDIENCE: z.string().min(1).default('hairloom-app'),
});

// WebAuthn configuration
const WebAuthnConfigSchema = z.object({
  WEBAUTHN_RP_ID: z.string().min(1, 'WEBAUTHN_RP_ID is required for passkey functionality'),
  WEBAUTHN_ORIGIN: z.string().min(1, 'WEBAUTHN_ORIGIN is required for passkey functionality'),
});

// Environment detection
const EnvironmentConfigSchema = z.object({
  VERCEL_ENV: z.enum(['development', 'preview', 'production']).optional(),
  NODE_ENV: z.enum(['development', 'test', 'production']).optional(),
});

// Complete configuration schema
export const SecretsConfigSchema = DatabaseConfigSchema
  .merge(EmailConfigSchema)
  .merge(SecurityConfigSchema)
  .merge(OAuthConfigSchema)
  .merge(WebAuthnConfigSchema)
  .merge(EnvironmentConfigSchema);

export type SecretsConfig = z.infer<typeof SecretsConfigSchema>;

/**
 * Validated environment configuration
 * This is the single source of truth for all environment variables
 */
let validatedConfig: SecretsConfig | null = null;

/**
 * Get validated environment configuration
 * Performs validation on first access and caches the result
 */
export function getSecretsConfig(): SecretsConfig {
  if (validatedConfig) {
    return validatedConfig;
  }

  try {
    validatedConfig = SecretsConfigSchema.parse(env);
    return validatedConfig;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues.map(issue => {
        const path = issue.path.join('.');
        return `${path}: ${issue.message}`;
      }).join('\n');

      throw new Error(
        `Environment variable validation failed:\n${errorMessages}\n\n` +
        `Please check your environment variables and ensure all required secrets are set.\n` +
        `For GitHub Secrets management, use the secrets CLI tool in tools/secrets-cli/`
      );
    }
    throw error;
  }
}

/**
 * Validate environment configuration at startup
 * Call this during application initialization to fail fast
 */
export function validateSecretsAtStartup(): void {
  try {
    const config = getSecretsConfig();
    console.log('✅ Environment variables validated successfully');
    
    // Log environment detection
    const environment = detectEnvironment(config);
    console.log(`🌍 Environment detected: ${environment}`);
    
    // Log optional features status
    logOptionalFeaturesStatus(config);
    
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    console.error('\n💡 Troubleshooting tips:');
    console.error('   1. Check your .env file for missing variables');
    console.error('   2. Verify Vercel environment variables are set');
    console.error('   3. Use the secrets CLI tool for GitHub Secrets management');
    console.error('   4. Refer to .env.example for required variables');
    
    // In production, exit the process
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
    
    throw error;
  }
}

/**
 * Detect current environment
 */
export function detectEnvironment(config?: SecretsConfig): 'development' | 'preview' | 'production' | 'local' {
  const cfg = config || getSecretsConfig();
  
  if (cfg.VERCEL_ENV) {
    return cfg.VERCEL_ENV;
  }
  
  if (cfg.NODE_ENV === 'production') {
    return 'production';
  }
  
  if (cfg.NODE_ENV === 'development') {
    return 'development';
  }
  
  return 'local';
}

/**
 * Check if we're in a development environment
 */
export function isDevelopment(): boolean {
  const environment = detectEnvironment();
  return environment === 'development' || environment === 'local';
}

/**
 * Check if we're in a production environment
 */
export function isProduction(): boolean {
  const environment = detectEnvironment();
  return environment === 'production';
}

/**
 * Check if we're in a preview environment
 */
export function isPreview(): boolean {
  const environment = detectEnvironment();
  return environment === 'preview';
}

/**
 * Log status of optional features based on available configuration
 */
function logOptionalFeaturesStatus(config: SecretsConfig): void {
  const features: Array<{ name: string; enabled: boolean; reason?: string }> = [];
  
  // Email functionality
  features.push({
    name: 'Email Service',
    enabled: !!(config.RESEND_API_KEY && config.EMAIL_FROM),
    reason: !config.RESEND_API_KEY ? 'RESEND_API_KEY missing' : !config.EMAIL_FROM ? 'EMAIL_FROM missing' : undefined,
  });
  
  // Google OAuth
  features.push({
    name: 'Google OAuth',
    enabled: !!(config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_SECRET),
    reason: !config.GOOGLE_CLIENT_ID ? 'GOOGLE_CLIENT_ID missing' : !config.GOOGLE_CLIENT_SECRET ? 'GOOGLE_CLIENT_SECRET missing' : undefined,
  });
  
  // GitHub OAuth
  features.push({
    name: 'GitHub OAuth',
    enabled: !!(config.GITHUB_CLIENT_ID && config.GITHUB_CLIENT_SECRET),
    reason: !config.GITHUB_CLIENT_ID ? 'GITHUB_CLIENT_ID missing' : !config.GITHUB_CLIENT_SECRET ? 'GITHUB_CLIENT_SECRET missing' : undefined,
  });
  
  // WebAuthn/Passkeys
  features.push({
    name: 'WebAuthn/Passkeys',
    enabled: !!(config.WEBAUTHN_RP_ID && config.WEBAUTHN_ORIGIN),
    reason: !config.WEBAUTHN_RP_ID ? 'WEBAUTHN_RP_ID missing' : !config.WEBAUTHN_ORIGIN ? 'WEBAUTHN_ORIGIN missing' : undefined,
  });
  
  console.log('\n🔧 Feature Status:');
  features.forEach(feature => {
    if (feature.enabled) {
      console.log(`   ✅ ${feature.name}: Enabled`);
    } else {
      console.log(`   ⚠️  ${feature.name}: Disabled (${feature.reason})`);
    }
  });
}

/**
 * Get database configuration
 */
export function getDatabaseConfig(): { POSTGRES_URL: string } {
  const config = getSecretsConfig();
  return {
    POSTGRES_URL: config.POSTGRES_URL,
  };
}

/**
 * Get email configuration
 */
export function getEmailConfig(): { RESEND_API_KEY: string; EMAIL_FROM: string } {
  const config = getSecretsConfig();
  return {
    RESEND_API_KEY: config.RESEND_API_KEY,
    EMAIL_FROM: config.EMAIL_FROM,
  };
}

/**
 * Get security configuration
 */
export function getSecurityConfig(): { ENCRYPTION_SECRET: string; TWO_FACTOR_AUTHENTICATION: string } {
  const config = getSecretsConfig();
  return {
    ENCRYPTION_SECRET: config.ENCRYPTION_SECRET,
    TWO_FACTOR_AUTHENTICATION: config.TWO_FACTOR_AUTHENTICATION,
  };
}

/**
 * Get JWT configuration
 * Validates against a separate schema so JWT secrets can be optional in the global config
 */
export function getJWTConfig(): { JWT_ACCESS_SECRET: string; JWT_REFRESH_SECRET: string; JWT_ISSUER: string; JWT_AUDIENCE: string } {
  const parsed = JWTConfigSchema.safeParse(env);
  if (!parsed.success) {
    // Provide a clearer error but avoid crashing unless actually needed
    const msg = parsed.error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join('; ');
    throw new Error(`JWT configuration invalid or missing: ${msg}`);
  }
  const cfg = parsed.data;
  return {
    JWT_ACCESS_SECRET: cfg.JWT_ACCESS_SECRET,
    JWT_REFRESH_SECRET: cfg.JWT_REFRESH_SECRET,
    JWT_ISSUER: cfg.JWT_ISSUER,
    JWT_AUDIENCE: cfg.JWT_AUDIENCE,
  };
}

/**
 * Get OAuth configuration
 */
export function getOAuthConfig(): {
  google?: { clientId: string; clientSecret: string; redirectUri?: string };
  github?: { clientId: string; clientSecret: string; redirectUri?: string };
} {
  const config = getSecretsConfig();
  
  const result: ReturnType<typeof getOAuthConfig> = {};
  
  if (config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_SECRET) {
    result.google = {
      clientId: config.GOOGLE_CLIENT_ID,
      clientSecret: config.GOOGLE_CLIENT_SECRET,
      redirectUri: config.GOOGLE_REDIRECT_URI,
    };
  }
  
  if (config.GITHUB_CLIENT_ID && config.GITHUB_CLIENT_SECRET) {
    result.github = {
      clientId: config.GITHUB_CLIENT_ID,
      clientSecret: config.GITHUB_CLIENT_SECRET,
      redirectUri: config.GITHUB_REDIRECT_URI,
    };
  }
  
  return result;
}

/**
 * Get WebAuthn configuration
 */
export function getWebAuthnConfig(): { rpId: string; origin: string[] } {
  const config = getSecretsConfig();
  return {
    rpId: config.WEBAUTHN_RP_ID,
    origin: config.WEBAUTHN_ORIGIN.split(',').map(o => o.trim()),
  };
}

/**
 * Utility to check if a specific secret is available
 */
export function hasSecret(secretName: keyof SecretsConfig): boolean {
  try {
    const config = getSecretsConfig();
    return !!(config[secretName]);
  } catch {
    return false;
  }
}

/**
 * Get all available secrets (for debugging/admin purposes)
 * Values are masked for security
 */
export function getSecretsStatus(): Record<string, { available: boolean; masked?: string }> {
  try {
    const config = getSecretsConfig();
    const status: Record<string, { available: boolean; masked?: string }> = {};
    
    Object.keys(config).forEach(key => {
      const value = config[key as keyof SecretsConfig];
      status[key] = {
        available: !!value,
        masked: value ? `${value.slice(0, 4)}...${value.slice(-4)}` : undefined,
      };
    });
    
    return status;
  } catch {
    return {};
  }
}
