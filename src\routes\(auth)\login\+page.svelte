<!--
This Svelte component represents the login/registration page.
It uses Svelte Runes for reactivity and $props() to access form data from server actions.
The `enhance` function from $app/forms is used for progressive enhancement of the form.
It displays a form with email and password inputs, and buttons for Login and Register actions.
Error messages from the server (form?.message) are displayed below the form.
-->
<script lang="ts">
	import UnifiedAuth from '$lib/components/auth/UnifiedAuth.svelte';
	import type { ActionData, PageData } from './$types';

	let { data, form }: { data: PageData; form?: ActionData } = $props();

	const socialProviders = [
		{ name: 'GitHub', url: '/login/github' },
		{ name: 'Google', url: '/login/google' }
	];
</script>

<!-- Login Page -->
<div class="min-h-screen bg-base-100 relative" data-testid="login-page">
	<!-- Background to match app aesthetic -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<div class="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-secondary/2"></div>
		<svg class="absolute inset-0 w-full h-full" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
			<defs>
				<linearGradient id="authGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
					<stop offset="0%" style="stop-color:oklch(65% 0.25 280);stop-opacity:0.1" />
					<stop offset="50%" style="stop-color:oklch(70% 0.22 320);stop-opacity:0.2" />
					<stop offset="100%" style="stop-color:oklch(75% 0.18 60);stop-opacity:0.1" />
				</linearGradient>
				<linearGradient id="authGradient2" x1="100%" y1="0%" x2="0%" y2="100%">
					<stop offset="0%" style="stop-color:oklch(70% 0.20 200);stop-opacity:0.1" />
					<stop offset="50%" style="stop-color:oklch(65% 0.25 240);stop-opacity:0.15" />
					<stop offset="100%" style="stop-color:oklch(75% 0.18 120);stop-opacity:0.1" />
				</linearGradient>
			</defs>
			<path d="M50,180 Q350,120 650,220 Q950,160 1150,200" stroke="url(#authGradient1)" stroke-width="2" fill="none" />
			<path d="M180,650 Q480,450 780,520 Q1080,390 1200,300" stroke="url(#authGradient2)" stroke-width="1.5" fill="none" />
		</svg>
		<div class="absolute inset-0 opacity-[0.02]" style="background-image: linear-gradient(oklch(65% 0.25 280) 1px, transparent 1px), linear-gradient(90deg, oklch(65% 0.25 280) 1px, transparent 1px); background-size: 60px 60px;"></div>
	</div>

	<!-- Content -->
	<div class="relative z-10 flex items-center justify-center min-h-screen p-4">
		<UnifiedAuth passkeyOptions={data.passkeyOptions} {socialProviders} {form} />
	</div>
</div>
