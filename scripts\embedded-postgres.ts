import fs from 'fs';
import path from 'path';
import EmbeddedPostgres from 'embedded-postgres';

// Simple embedded Postgres lifecycle manager for local development.
// Requirements:
// - Works on Windows/macOS/Linux without Docker/WSL
// - Downloads binaries on first run, then works offline
// - Persists data by default under .embedded-pg/data
// - Start keeps the process alive until Ctrl+C; Stop attempts graceful shutdown

const DATA_DIR = process.env.EMBEDDED_PG_DATA_DIR || path.resolve('.embedded-pg/data');
const PORT = parseInt(process.env.EMBEDDED_PG_PORT || '5452', 10);
const USER = process.env.EMBEDDED_PG_USER || 'postgres';
const PASSWORD = process.env.EMBEDDED_PG_PASSWORD || 'hairloomdev';
const DB_NAME = process.env.EMBEDDED_PG_DB || 'hairloom';
const STATE_DIR = path.resolve('.embedded-pg');
const STATE_FILE = path.join(STATE_DIR, 'state.json');

function ensureDir(dir: string) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function createPg() {
  const pg = new EmbeddedPostgres({
    databaseDir: DATA_DIR,
    user: USER,
    password: PASSWORD,
    port: PORT,
    persistent: true,
    // authMethod: 'scram-sha-256', // optional; default 'password' works fine for dev
  });
  return pg;
}

async function start() {
  ensureDir(STATE_DIR);
  ensureDir(DATA_DIR);

  const pg = createPg();
  console.log(`[embedded-pg] Initializing cluster in ${DATA_DIR} on port ${PORT} ...`);
  await pg.initialise();

  console.log('[embedded-pg] Starting Postgres ...');
  await pg.start();

  try {
    await pg.createDatabase(DB_NAME);
  } catch (e) {
    // Ignore if database already exists
  }

  const url = `postgres://${encodeURIComponent(USER)}:${encodeURIComponent(PASSWORD)}@localhost:${PORT}/${DB_NAME}`;
  const state = { dataDir: DATA_DIR, port: PORT, user: USER, db: DB_NAME, url };
  fs.writeFileSync(STATE_FILE, JSON.stringify(state, null, 2));

  console.log('[embedded-pg] Ready. Connection string:');
  console.log(url);
  console.log('[embedded-pg] Press Ctrl+C to stop.');

  const shutdown = async () => {
    console.log('\n[embedded-pg] Stopping Postgres ...');
    try {
      await pg.stop();
      console.log('[embedded-pg] Stopped.');
    } catch (err) {
      console.error('[embedded-pg] Error during stop:', err);
    } finally {
      process.exit(0);
    }
  };

  process.on('SIGINT', shutdown);
  process.on('SIGTERM', shutdown);

  // Keep process alive
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  await new Promise<void>(() => {});
}

function readPostmasterPid(): number | null {
  // postmaster.pid lives in the data directory root
  const pidFile = path.join(DATA_DIR, 'postmaster.pid');
  if (!fs.existsSync(pidFile)) return null;
  try {
    const content = fs.readFileSync(pidFile, 'utf8');
    const firstLine = content.split(/\r?\n/)[0]?.trim();
    const pid = parseInt(firstLine, 10);
    return Number.isFinite(pid) ? pid : null;
  } catch {
    return null;
  }
}

async function stop() {
  const pg = createPg();
  let stopped = false;
  try {
    await pg.stop();
    stopped = true;
    console.log('[embedded-pg] Stopped using library handle.');
  } catch (err) {
    console.warn('[embedded-pg] Library stop failed or not running, attempting PID kill ...');
  }

  if (!stopped) {
    const pid = readPostmasterPid();
    if (pid) {
      try {
        process.kill(pid);
        console.log(`[embedded-pg] Killed postmaster PID ${pid}.`);
        stopped = true;
      } catch (err) {
        console.error('[embedded-pg] Failed to kill PID', pid, err);
      }
    } else {
      console.log('[embedded-pg] No running postmaster.pid found.');
    }
  }

  if (fs.existsSync(STATE_FILE)) {
    try { fs.unlinkSync(STATE_FILE); } catch {}
  }
}

async function main() {
  const cmd = process.argv[2] || 'start';
  if (cmd === 'start') return start();
  if (cmd === 'stop') return stop();
  console.error('Usage: tsx scripts/embedded-postgres.ts [start|stop]');
  process.exit(1);
}

main().catch((err) => {
  console.error('[embedded-pg] Fatal error:', err);
  process.exit(1);
});

