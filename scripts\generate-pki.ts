#!/usr/bin/env tsx
import fs from 'fs';
import os from 'os';
import path from 'path';
import { spawnSync } from 'child_process';

/*
  Local PKI helper using OpenSSL.
  - Cross-platform (requires openssl in PATH)
  - Generates a self-signed cert with SAN for local HTTPS development
  - Outputs PEM key/cert pair under ./certs by default

  Usage examples:
    tsx scripts/generate-pki.ts
    tsx scripts/generate-pki.ts --domain localhost --domain 127.0.0.1 --domain ::1
    tsx scripts/generate-pki.ts --domain ************.nip.io --out certs/192-168-2-10.nip.io

  Resulting files:
    <outBase>-key.pem  (private key)
    <outBase>.pem      (certificate)
*/

function hasOpenSSL(): boolean {
  const res = spawnSync('openssl', ['version'], { stdio: 'pipe' });
  return res.status === 0;
}

function parseArgs(argv: string[]) {
  const domains: string[] = [];
  let outBase = '';
  for (let i = 2; i < argv.length; i++) {
    const arg = argv[i];
    if (arg === '--domain' && argv[i + 1]) {
      domains.push(argv[++i]);
    } else if ((arg === '--out' || arg === '--outBase') && argv[i + 1]) {
      outBase = argv[++i];
    }
  }
  if (domains.length === 0) {
    domains.push('localhost', '127.0.0.1', '::1');
  }
  if (!outBase) {
    const primary = domains[0].replace(/[^a-zA-Z0-9_.-]/g, '-');
    outBase = path.join('certs', primary);
  }
  return { domains, outBase };
}

function ensureDir(filePath: string) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function buildOpenSSLConfig(domains: string[], commonName: string): string {
  const sanLines: string[] = [];
  let dnsIndex = 1;
  let ipIndex = 1;
  for (const d of domains) {
    // naive IP check
    const isIPv4 = /^\d+\.\d+\.\d+\.\d+$/.test(d);
    const isIPv6 = /:/.test(d);
    if (isIPv4 || isIPv6) {
      sanLines.push(`IP.${ipIndex++} = ${d}`);
    } else {
      sanLines.push(`DNS.${dnsIndex++} = ${d}`);
    }
  }

  return `
[ req ]
default_bits       = 2048
prompt             = no
default_md         = sha256
distinguished_name = dn
x509_extensions    = v3_req

[ dn ]
CN = ${commonName}

[ v3_req ]
subjectAltName = @alt_names
basicConstraints = critical, CA:false
keyUsage = critical, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth

[ alt_names ]
${sanLines.join('\n')}
`.trimStart();
}

function generateCert(outBase: string, domains: string[]) {
  const keyPath = `${outBase}-key.pem`;
  const certPath = `${outBase}.pem`;
  ensureDir(keyPath);

  const tmpDir = fs.mkdtempSync(path.join(os.tmpdir(), 'local-pki-'));
  const cnfPath = path.join(tmpDir, 'openssl.cnf');
  const commonName = domains[0];
  const cnf = buildOpenSSLConfig(domains, commonName);
  fs.writeFileSync(cnfPath, cnf, 'utf8');

  // Single command to create key and self-signed cert with SANs
  const args = [
    'req',
    '-x509',
    '-newkey', 'rsa:2048',
    '-sha256',
    '-days', '825',
    '-nodes',
    '-keyout', keyPath,
    '-out', certPath,
    '-config', cnfPath,
    '-extensions', 'v3_req',
  ];

  const res = spawnSync('openssl', args, { stdio: 'inherit' });
  if (res.status !== 0) {
    throw new Error('OpenSSL failed to generate certificate');
  }

  try {
    fs.chmodSync(keyPath, 0o600);
  } catch {}

  return { keyPath, certPath };
}

function main() {
  if (!hasOpenSSL()) {
    console.error('Error: openssl is not available on PATH.');
    console.error('Please install OpenSSL or use an alternative like mkcert.');
    console.error('Windows (winget): winget install ShiningLight.OpenSSL');
    process.exit(1);
  }

  const { domains, outBase } = parseArgs(process.argv);
  console.log(`Generating self-signed certificate for: ${domains.join(', ')}`);
  const { keyPath, certPath } = generateCert(outBase, domains);

  console.log('\nSuccess! Files written:');
  console.log(`  Key:  ${keyPath}`);
  console.log(`  Cert: ${certPath}`);
  console.log('\nConfigure your dev server to use these files.');
}

main();

