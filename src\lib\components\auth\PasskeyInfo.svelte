<script lang="ts">
  let { inline = false }: { inline?: boolean } = $props();
</script>

{#if inline}
  <div class="tooltip" data-tip="Passkeys are device-bound sign-ins — faster, phishing-resistant, and no passwords to remember.">
    <span class="cursor-help text-base-content/60">What’s a passkey?</span>
  </div>
{:else}
  <div class="alert alert-info shadow-sm">
    <div>
      <span>🔑</span>
      <div>
        <h3 class="font-semibold">Passkeys keep you safe and make sign-in effortless</h3>
        <p class="text-sm opacity-80">They use your device’s secure hardware. No password reuse, no phishing.</p>
      </div>
    </div>
  </div>
{/if}

