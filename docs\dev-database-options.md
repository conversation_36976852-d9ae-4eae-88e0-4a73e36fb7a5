# Development Database Options

This project supports three local development database modes, selected via `DB_MODE`:

- docker (primary): Local PostgreSQL via Docker Compose
- embedded (fallback): Embedded PostgreSQL started by <PERSON>de, no Docker/WSL required
- remote (online): Hosted PostgreSQL (e.g., Neon dev branch)

All options use the same Drizzle schema and migration/seed workflows and the same `POSTGRES_URL` at runtime (resolved by a wrapper script).

## Quick start

1. Copy `.env.example` to `.env.development.local` and adjust values as needed. Set `DB_MODE` there (default is `docker`).
2. Start your DB:
   - Docker: `npm run db:start` (first pull requires internet)
   - Embedded: `npm run db:embedded:start` (first run downloads binaries)
   - Remote: ensure the remote DB is reachable and set `POSTGRES_URL_REMOTE`
3. Apply migrations and seed:
   - `npm run db:migrate`
   - `npm run db:seed`
4. Run the app:
   - `npm run dev`

Tip: You can temporarily switch modes without editing the env file by prefixing commands, e.g., `DB_MODE=embedded npm run dev` (use `cross-env DB_MODE=embedded` on Windows).

The `scripts/with-db.ts` wrapper sets `POSTGRES_URL` based on `DB_MODE` and the URLs in your env file for all DB scripts and the dev server.

## Option 1 — Docker Compose (primary)
- Mode: `DB_MODE=docker`
- URL: `POSTGRES_URL_DOCKER`
- Commands:
  - Start: `npm run db:start`
  - Stop: `npm run db:stop`
- Remarks:
  - Works fully offline after image is pulled once.
  - Matches production Postgres closely. Consider pinning version in `docker-compose.yml` if needed.

## Option 2 — Embedded PostgreSQL (fallback)
- Mode: `DB_MODE=embedded`
- URL: `POSTGRES_URL_EMBEDDED`
- Commands:
  - Start: `npm run db:embedded:start` (keeps process running; Ctrl+C to stop)
  - Stop: `npm run db:embedded:stop`
- Config (optional): `EMBEDDED_PG_PORT`, `EMBEDDED_PG_USER`, `EMBEDDED_PG_PASSWORD`, `EMBEDDED_PG_DB`, `EMBEDDED_PG_DATA_DIR`
- Notes:
  - On first run, the package downloads platform-specific binaries (requires internet once; then offline).
  - Data persists under `.embedded-pg/data` by default.

## Option 3 — Remote PostgreSQL (online)
- Mode: `DB_MODE=remote`
- URL: `POSTGRES_URL_REMOTE`
- Remarks:
  - Requires internet connectivity.
  - Useful for team collaboration or when local resources are constrained.

## Migrations and seeding
- Drizzle migrations are under `./drizzle`. Use:
  - `npm run db:generate` to generate from schema
  - `npm run db:push` or `npm run db:migrate` to apply
  - `npm run db:seed` to seed dev data, `npm run db:seed:prod` for prod-safe seed
- Reset + reseed: `npm run db:reseed` (dev/preview only; guarded against prod)

## How URL selection works
- The app code expects `POSTGRES_URL`.
- We do not change runtime DB code; instead we run commands via `scripts/with-db.ts` which:
  1. Reads `DB_MODE` (default `docker`)
  2. Picks `POSTGRES_URL_DOCKER` | `POSTGRES_URL_EMBEDDED` | `POSTGRES_URL_REMOTE`
  3. Exports it as `POSTGRES_URL` for the spawned command
- You can always override by setting `POSTGRES_URL` explicitly in your environment.

## When to use each mode
- docker: Default for most developers; highest parity, offline after first pull.
- embedded: For Windows machines without Docker/WSL or installs; offline after first run.
- remote: For collaboration on a shared dev DB or when local resources are constrained.

## Troubleshooting
- Migrations complain about `POSTGRES_URL`
  - Ensure you are running commands via our npm scripts (they wrap with `with-db.ts`), or set `POSTGRES_URL` explicitly.
- Port in use
  - Change `EMBEDDED_PG_PORT` or Docker port mapping accordingly.
- First run needs internet
  - Docker image pull or embedded binary download happens once; subsequent runs are offline.

