import { createInsertSchema, createSelectSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod/v4';

// Drizzle table definitions
import {
	client_notes,
	clients,
	expiring_rate_limit_buckets,
	rate_limit_buckets,
	templates,
	users
} from './schema';

// ---------------------------------------------------------------------------
// Automatically generated Zod schemas from Drizzle ORM definitions.
// These closely follow the column types + nullability expressed in the pgTable
// declarations and give you a single source of truth for validating data at
// API boundaries.
// ---------------------------------------------------------------------------

// Users ---------------------------------------------------------------------
export const userSelectSchema = createSelectSchema(users);
export const userInsertSchema = createInsertSchema(users, {
	email: (schema: z.ZodString) => z.email().max(255),
	first_name: (schema: z.ZodString) => schema.max(100),
	last_name: (schema: z.ZodString) => schema.max(100)
});
export const userUpdateSchema = createUpdateSchema(users);

// Clients -------------------------------------------------------------------
export const clientSelectSchema = createSelectSchema(clients);
export const clientInsertSchema = createInsertSchema(clients, {
	first_name: (schema) => schema.max(100),
	last_name: (schema) => schema.max(100),
	email: (schema) => z.email().max(255)
});
export const clientUpdateSchema = createUpdateSchema(clients);

// Templates -----------------------------------------------------------------
export const templateSelectSchema = createSelectSchema(templates);
export const templateInsertSchema = createInsertSchema(templates);
export const templateUpdateSchema = createUpdateSchema(templates);
export type FormTemplate = z.infer<typeof templateSelectSchema>;

// Client Notes --------------------------------------------------------------
export const clientNoteSelectSchema = createSelectSchema(client_notes);
export const clientNoteInsertSchema = createInsertSchema(client_notes);
export const clientNoteUpdateSchema = createUpdateSchema(client_notes);

// Rate-limit buckets ---------------------------------------------------------
export const rateLimitBucketSelectSchema = createSelectSchema(rate_limit_buckets);
export const rateLimitBucketInsertSchema = createInsertSchema(rate_limit_buckets);
export const rateLimitBucketUpdateSchema = createUpdateSchema(rate_limit_buckets);

// Expiring rate-limit buckets ----------------------------------------------
export const expiringRateLimitBucketSelectSchema = createSelectSchema(expiring_rate_limit_buckets);
export const expiringRateLimitBucketInsertSchema = createInsertSchema(expiring_rate_limit_buckets);
export const expiringRateLimitBucketUpdateSchema = createUpdateSchema(expiring_rate_limit_buckets);

// ---------------------------------------------------------------------------
// Helpers -------------------------------------------------------------------
// ---------------------------------------------------------------------------

/**
 * Given safe-parsed data from one of the above *_InsertSchema schemas, coerce
 * optional empty strings to `null` so they match the DB's expectations and
 * avoid violating NOT NULL constraints for columns that allow nulls in Drizzle.
 */
export function normalizeEmptyStrings<T extends Record<string, unknown>>(data: T): T {
	return Object.fromEntries(Object.entries(data).map(([k, v]) => [k, v === '' ? null : v])) as T;
}
