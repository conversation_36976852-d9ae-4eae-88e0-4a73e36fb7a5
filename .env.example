# Database configuration for multi-environment local dev
# Select one of: docker | embedded | remote
DB_MODE=docker

# Option 1: Docker Compose Postgres (default)
POSTGRES_URL_DOCKER="postgres://postgres:hairloomdev@localhost:5451/hairloom"

# Option 2: Embedded Postgres (pg) via scripts/embedded-postgres.ts
# Will listen on 5452 by default
POSTGRES_URL_EMBEDDED="postgres://postgres:hairloomdev@localhost:5452/hairloom"
# Optional embedded settings
EMBEDDED_PG_PORT=5452
EMBEDDED_PG_USER=postgres
EMBEDDED_PG_PASSWORD=hairloomdev
EMBEDDED_PG_DB=hairloom
# EMBEDDED_PG_DATA_DIR=.embedded-pg/data

# Option 3: Remote Postgres (e.g., Neon dev branch)
# Fill this in when using DB_MODE=remote
POSTGRES_URL_REMOTE=""

# If POSTGRES_URL is set explicitly it overrides all of the above
# POSTGRES_URL=

# App environment
NODE_ENV=development
PUBLIC_VERCEL_ENV=development

# WebAuthn for local dev
WEBAUTHN_RP_ID=localhost
WEBAUTHN_ORIGIN=http://localhost:5173

# Emails / crypto (set real values as needed)
RESEND_API_KEY=
EMAIL_FROM=<EMAIL>
ENCRYPTION_SECRET=change-me
TWO_FACTOR_AUTHENTICATION=disabled
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
