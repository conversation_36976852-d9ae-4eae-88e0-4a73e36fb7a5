<!--
  ConfirmationModal.svelte
  A global modal component to confirm user actions.
  It is controlled by the `confirmationStore`, which can be accessed from anywhere in the application.
  This allows any component to trigger a confirmation prompt without complex prop drilling or event dispatching.
-->
<script lang="ts">
	import { confirmationStore } from '$lib/stores/confirmationStore.svelte';

	function handleConfirm() {
		confirmationStore.close(true);
	}

	function handleCancel() {
		confirmationStore.close(false);
	}
</script>

{#if confirmationStore.isOpen && confirmationStore.config}
	<dialog id="confirmation_modal" class="modal modal-open modal-bottom sm:modal-middle" open>
		<div class="modal-box">
			<h3 class="text-lg font-bold">{confirmationStore.config.title}</h3>
			<p class="py-4">{confirmationStore.config.message}</p>
			<div class="modal-action">
				<form method="dialog" class="flex w-full gap-2">
					<button class="btn flex-1" onclick={handleCancel}>
						{confirmationStore.config.cancelText || 'Cancel'}
					</button>
					<button
						class="btn flex-1 {confirmationStore.config.confirmClass || 'btn-primary'}"
						onclick={handleConfirm}
					>
						{confirmationStore.config.confirmText || 'Confirm'}
					</button>
				</form>
			</div>
		</div>
		<form method="dialog" class="modal-backdrop">
			<button type="button" onclick={handleCancel}>close</button>
		</form>
	</dialog>
{/if}
