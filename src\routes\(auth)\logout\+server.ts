import { deleteSessionTokenCookie, invalidateSession } from '$lib/server/auth';
import { clearJWTCookies } from '$lib/server/auth/jwt';
import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types'; // Changed from Actions

// GET handler for logout, e.g. from a link
export const GET: RequestHandler = async (event) => {
	const { session } = event.locals;
	if (session) {
		await invalidateSession(session.id);
		deleteSessionTokenCookie(event);
		clearJWTCookies(event);
	}
	redirect(302, '/login');
};

// POST handler for logout, e.g. from a form submission
export const POST: RequestHandler = async (event) => {
	if (event.locals.session) {
		await invalidateSession(event.locals.session.id);
		deleteSessionTokenCookie(event);
		clearJWTCookies(event);
	}
	redirect(302, '/login');
};
