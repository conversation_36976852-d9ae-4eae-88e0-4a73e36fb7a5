#!/usr/bin/env tsx
import fs from 'fs';
import path from 'path';
import os from 'os';
import { spawnSync } from 'child_process';

/*
  Local HTTPS certificates via mkcert (primary: localhost)
  -------------------------------------------------------
  - Uses mkcert (https://github.com/FiloSottile/mkcert) to generate locally trusted certs
  - Installs a local CA on first run (mkcert -install)
  - Defaults to localhost SANs (localhost, 127.0.0.1, ::1)
  - Outputs PEM key/cert pair to ./certs by default

  Prerequisites (install mkcert):
    macOS:   brew install mkcert nss   # nss only if using Firefox
    Windows: choco install mkcert  OR  winget install FiloSottile.mkcert
    Linux:   Follow mkcert README for your distro (may need libnss3-tools)

  Usage examples:
    tsx scripts/generate-mkcert.ts                       # default localhost SANs
    tsx scripts/generate-mkcert.ts --lan 192-168-2-10    # single LAN domain
    tsx scripts/generate-mkcert.ts --lan 192-168-2-10 --with-localhost  # LAN + localhost
    tsx scripts/generate-mkcert.ts --domain my.dev.local --with-localhost

  Resulting files:
    <outBase>-key.pem  (private key)
    <outBase>.pem      (certificate)
*/

function hasMkcert(): boolean {
  const res = spawnSync('mkcert', ['-help'], { stdio: 'pipe' });
  return res.status === 0;
}

function parseArgs(argv: string[]) {
  const domains: string[] = [];
  let outBase = '';
  let doInstall = true;
  let withLocalhost = false;

  for (let i = 2; i < argv.length; i++) {
    const arg = argv[i];
    if (arg === '--domain' && argv[i + 1]) {
      domains.push(argv[++i]);
    } else if ((arg === '--out' || arg === '--outBase') && argv[i + 1]) {
      outBase = argv[++i];
    } else if (arg === '--no-install') {
      doInstall = false;
    } else if (arg === '--with-localhost') {
      withLocalhost = true;
    } else if (arg === '--lan' && argv[i + 1]) {
      // convenience: build a nip.io domain from the arg if it looks like an IP
      const lan = argv[++i];
      const nip = lan.match(/\d+\.\d+\.\d+\.\d+/) ? `${lan}.nip.io` : lan;
      domains.push(nip);
      if (!outBase) outBase = path.join('certs', nip);
    }
  }

  if (domains.length === 0) {
    // default primary dev certs: localhost
    domains.push('localhost', '127.0.0.1', '::1');
    outBase = outBase || path.join('certs', 'localhost');
  }

  if (withLocalhost) {
    for (const d of ['localhost', '127.0.0.1', '::1']) {
      if (!domains.includes(d)) domains.push(d);
    }
  }

  return { domains, outBase, doInstall };
}

function ensureDir(filePath: string) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

function runMkcertInstall() {
  const res = spawnSync('mkcert', ['-install'], { stdio: 'inherit' });
  if (res.status !== 0) {
    throw new Error('mkcert -install failed. You may need admin privileges or to install mkcert properly.');
  }
}

function generateWithMkcert(outBase: string, domains: string[]) {
  ensureDir(outBase);
  const keyPath = `${outBase}-key.pem`;
  const certPath = `${outBase}.pem`;

  const args = [
    '-key-file', keyPath,
    '-cert-file', certPath,
    ...domains,
  ];

  const res = spawnSync('mkcert', args, { stdio: 'inherit' });
  if (res.status !== 0) {
    throw new Error('mkcert certificate generation failed');
  }

  try { fs.chmodSync(keyPath, 0o600); } catch {}
  return { keyPath, certPath };
}

function main() {
  if (!hasMkcert()) {
    console.error('Error: mkcert is not installed or not on PATH.');
    console.error('Install mkcert: https://github.com/FiloSottile/mkcert#installation');
    console.error('macOS: brew install mkcert nss');
    console.error('Windows: choco install mkcert  OR  winget install FiloSottile.mkcert');
    console.error('Linux: see README; you may need libnss3-tools');
    process.exit(1);
  }

  const { domains, outBase, doInstall } = parseArgs(process.argv);
  console.log(`Using mkcert for domains: ${domains.join(', ')}`);

  if (doInstall) {
    console.log('Ensuring local CA is installed (mkcert -install) ...');
    runMkcertInstall();
  } else {
    console.log('Skipping mkcert -install (per --no-install)');
  }

  const { keyPath, certPath } = generateWithMkcert(outBase, domains);

  console.log('\nSuccess! Files written:');
  console.log(`  Key:  ${path.resolve(keyPath)}`);
  console.log(`  Cert: ${path.resolve(certPath)}`);
  console.log('\nUpdate your dev server HTTPS config to point at these files.');
}

main();

