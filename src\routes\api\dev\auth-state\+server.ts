import { json, error, type RequestHandler } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { ACCESS_TOKEN_COOKIE, REFRESH_TOKEN_COOKIE, REAUTH_TOKEN_COOKIE, verifyAccessToken, verifyRef<PERSON>Token, verifyReauthToken } from '$lib/server/auth/jwt';

export const GET: RequestHandler = async (event) => {
  // Restrict to dev/preview environments only
  const isDev = env.VERCEL_ENV === 'development' || env.VERCEL_ENV === 'preview' || env.NODE_ENV === 'development';
  if (!isDev) throw error(403, 'Not available');

  const { cookies, locals } = event;

  const sessionCookie = cookies.get('auth-session');
  const accessCookie = cookies.get(ACCESS_TOKEN_COOKIE);
  const refreshCookie = cookies.get(REFRESH_TOKEN_COOKIE);
  const reauthCookie = cookies.get(REAUTH_TOKEN_COOKIE);

  let access: { exp?: number; sid?: string } | null = null;
  let refresh: { exp?: number; sid?: string; ver?: number } | null = null;
  let reauth: { exp?: number } | null = null;

  // Verify tokens if present; ignore verification errors to avoid leaking stack traces
  if (accessCookie) {
    try {
      const claims = await verifyAccessToken(accessCookie);
      access = { exp: claims.exp, sid: (claims as any).sid };
    } catch {}
  }
  if (refreshCookie) {
    try {
      const claims = await verifyRefreshToken(refreshCookie);
      refresh = { exp: claims.exp, sid: (claims as any).sid, ver: (claims as any).ver };
    } catch {}
  }
  if (reauthCookie) {
    try {
      const claims = await verifyReauthToken(reauthCookie);
      reauth = { exp: claims.exp };
    } catch {}
  }

  const responseBody = {
    user: locals.user ? { id: locals.user.id, email: locals.user.email } : null,
    session: locals.session
      ? { present: true, expiresAt: locals.session.expires_at.toISOString() }
      : { present: false },
    cookies: {
      sessionCookiePresent: !!sessionCookie,
      accessCookiePresent: !!accessCookie,
      refreshCookiePresent: !!refreshCookie,
      reauthCookiePresent: !!reauthCookie
    },
    tokens: {
      access,
      refresh,
      reauth
    }
  } as const;

  return json(responseBody);
};

