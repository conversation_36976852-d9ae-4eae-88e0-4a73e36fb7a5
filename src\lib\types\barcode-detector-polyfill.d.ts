declare module 'barcode-detector-polyfill' {
  interface BarcodeDetectorOptions {
    formats?: string[];
  }

  interface DetectedBarcode {
    boundingBox: DOMRectReadOnly;
    cornerPoints: { x: number; y: number }[];
    format: string;
    rawValue: string;
  }

  interface BarcodeDetectorConstructor {
    new (options?: BarcodeDetectorOptions): {
      detect(source: ImageBitmapSource): Promise<DetectedBarcode[]>;
    };
    getSupportedFormats?(): Promise<string[]>;
  }

  export const BarcodeDetectorPolyfill: BarcodeDetectorConstructor;
}

