<script lang="ts">
	// Simple test hub that links to existing dev pages.
	const tools = [
		{ href: '/test/barcode-scanner', title: 'Barcode Scanner Demo', desc: 'Try the barcode scanner component' },
		{ href: '/test/client-summary', title: 'Client Summary Demo', desc: 'View client summary test page' },
		{ href: '/test/schema-maker', title: 'Schema Maker Demo', desc: 'Prototype schema maker' }
	];
</script>

<div class="container mx-auto p-6">
	<h1 class="text-2xl font-bold mb-4">Developer Test Hub</h1>
	<p class="mb-6 text-base-content/70">Quick links to internal testing pages.</p>
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
		{#each tools as t}
			<a class="card bg-base-100 shadow hover:shadow-lg transition-shadow" href={t.href}>
				<div class="card-body">
					<h2 class="card-title">{t.title}</h2>
					<p class="text-sm opacity-70">{t.desc}</p>
				</div>
			</a>
		{/each}
	</div>
</div>

