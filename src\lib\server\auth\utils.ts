import { hash as argon2Hash, verify as argon2Verify } from '@node-rs/argon2';
import { sha256 } from '@oslojs/crypto/sha2';
import { encodeBase32UpperCaseNoPadding, encodeHexLowerCase } from '@oslojs/encoding';

export function generateRandomOTP(): string {
	const bytes = new Uint8Array(5);
	crypto.getRandomValues(bytes);
	const code = encodeBase32UpperCaseNoPadding(bytes);
	return code;
}

export function generateRandomRecoveryCode(): string {
	const recoveryCodeBytes = new Uint8Array(10);
	crypto.getRandomValues(recoveryCodeBytes);
	const recoveryCode = encodeBase32UpperCaseNoPadding(recoveryCodeBytes);
	return recoveryCode;
}

export function generateRandomUrlToken(byteLength: number = 24): string {
	const bytes = new Uint8Array(byteLength);
	crypto.getRandomValues(bytes);
	return encodeHexLowerCase(bytes);
}

export async function hashOtp(otp: string): Promise<string> {
	const otpBuffer = new TextEncoder().encode(otp);
	const hashedBuffer = await sha256(otpBuffer);
	return encodeHexLowerCase(hashedBuffer);
}

export async function hashRecoveryCode(recoveryCode: string): Promise<string> {
	return await argon2Hash(recoveryCode, {
		memoryCost: 19456,
		timeCost: 2,
		outputLen: 32,
		parallelism: 1
	});
}

export async function verifyRecoveryCodeHash(
	hashedCode: string,
	plaintextCode: string
): Promise<boolean> {
	return await argon2Verify(hashedCode, plaintextCode);
}
