import * as auth from '$lib/server/auth';
import { ACCESS_TOKEN_COOKIE, verifyAccessToken } from '$lib/server/auth/jwt';
import { themes, type Theme } from '$lib/stores/themeStore';
import type { Handle } from '@sveltejs/kit';
import { error } from '@sveltejs/kit';

const defaultTheme: Theme = 'light';
const themeCookieName = 'theme_preference';

export const handle: Handle = async ({ event, resolve }) => {
	// CSRF Protection for state-changing requests
	if (event.request.method === 'POST' && event.route.id?.startsWith('/(app)')) {
		const origin = event.request.headers.get('origin');
		const host = event.request.headers.get('host');

		// Allow same-origin requests and explicitly trusted origins
		if (!origin || (origin !== `https://${host}` && origin !== `http://${host}`)) {
			// Only allow localhost for development
			if (!origin?.startsWith('http://localhost:') && !origin?.startsWith('https://localhost:')) {
				throw error(403, 'CSRF: Invalid origin');
			}
		}
	}

	// --- Start of Authentication Logic (from original hook) ---
	const sessionToken = event.cookies.get(auth.sessionCookieName);

	if (!sessionToken) {
		// Fallback to JWT access token if present
		const accessToken = event.cookies.get(ACCESS_TOKEN_COOKIE);
		if (accessToken) {
			try {
				const claims = await verifyAccessToken(accessToken);
				if (claims.sid) {
					const result = await auth.getSessionAndUser(claims.sid);
					if (result) {
						// Keep locals consistent with session-based flow
						event.locals.user = result.user;
						event.locals.session = result.session;
					} else {
						event.locals.user = null;
						event.locals.session = null;
					}
				} else {
					event.locals.user = null;
					event.locals.session = null;
				}
			} catch {
				event.locals.user = null;
				event.locals.session = null;
			}
		} else {
			event.locals.user = null;
			event.locals.session = null;
		}
		// No return here yet, proceed to theme logic then resolve
	} else {
		const { session, user } = await auth.validateSessionToken(sessionToken);

		if (session) {
			auth.setSessionTokenCookie(event, sessionToken, session.expires_at);
		} else {
			auth.deleteSessionTokenCookie(event);
		}

		event.locals.user = user;
		event.locals.session = session;
	}
	// --- End of Authentication Logic ---

	// --- Start of Theme Logic ---
	const currentThemeCookieValue = event.cookies.get(themeCookieName);
	let themeToApply: Theme = defaultTheme;

	if (currentThemeCookieValue && themes.includes(currentThemeCookieValue as Theme)) {
		themeToApply = currentThemeCookieValue as Theme;
	}
	// --- End of Theme Logic ---

	const response = await resolve(event, {
		transformPageChunk: ({ html }) => {
			// Replace %sveltekit.html_attributes% with the data-theme attribute
			// SvelteKit will place other attributes like lang="en" from app.html itself.
			return html.replace('%sveltekit.html_attributes%', `data-theme="${themeToApply}"`);
		}
	});

	return response;
};
