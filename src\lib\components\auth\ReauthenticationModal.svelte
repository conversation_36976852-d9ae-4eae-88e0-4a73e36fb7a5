<!--
	@component
	This modal provides a unified way to re-authenticate a user for sensitive actions.
	It checks the user's available authentication methods (password, passkey) and
	presents the appropriate options.

	- If both password and passkeys are available, it offers a choice.
	- If only one method is available, it presents that method directly.
	- It handles the client-side logic for passkey authentication challenges.
	- It communicates success or failure back to the parent component via a promise.
-->
<script lang="ts">
	import { page } from '$app/stores';
	import Errors from '$lib/runes-form/Errors.svelte';
	import Field from '$lib/runes-form/Field.svelte';
	import RuneForm from '$lib/runes-form/RuneForm.svelte';
	import { createRuneForm } from '$lib/runes-form/index.svelte';
	import { authenticatePasskey } from '$lib/utils/webauthn.client';
	import { z } from 'zod/v4';
	import IconKey from '~icons/icon-park-outline/key';

	type Props = {
		show: boolean;
		action: string; // The form action to submit to
		title: string;
		message: string;
		onClose: (result: { success: boolean; payload?: any }) => void;
	};

	let { show, action, title, message, onClose }: Props = $props();

	const { hasPassword, hasPasskeys } = $page.data;
	let selectedAuthMethod = $state(hasPasskeys ? 'passkey' : 'password');

	const passwordSchema = z.object({
		password: z.string().min(1, 'Password is required.')
	});
	const passwordForm = createRuneForm(passwordSchema, { password: '' });

	let isLoading = $state(false);
	let errorMessage = $state<string | null>(null);

	async function handlePasskeyAuth() {
		isLoading = true;
		errorMessage = null;
		try {
			const authentication = await authenticatePasskey();
			onClose({ success: true, payload: { authMethod: 'passkey', authentication } });
		} catch (error) {
			console.error('Passkey authentication failed', error);
			errorMessage = 'Passkey authentication failed. Please try again or use your password.';
		} finally {
			isLoading = false;
		}
	}

	async function handlePasswordSubmit(values: { password: any }) {
		isLoading = true;
		errorMessage = null;
		onClose({ success: true, payload: { authMethod: 'password', password: values.password } });
		isLoading = false;
	}

	function handleCancel() {
		onClose({ success: false });
	}

	$effect(() => {
		if (show) {
			passwordForm.reset();
			errorMessage = null;
			selectedAuthMethod = hasPasskeys ? 'passkey' : 'password';
		}
	});
</script>

{#if show}
	<div class="modal modal-open">
		<div class="modal-box">
			<h3 class="text-lg font-bold">{title}</h3>
			<p class="py-4">{message}</p>

			{#if hasPasskeys && hasPassword}
				<div class="tabs tabs-boxed mb-4">
					<button
						class="tab"
						class:tab-active={selectedAuthMethod === 'passkey'}
						onclick={() => (selectedAuthMethod = 'passkey')}>Passkey</button
					>
					<button
						class="tab"
						class:tab-active={selectedAuthMethod === 'password'}
						onclick={() => (selectedAuthMethod = 'password')}>Password</button
					>
				</div>
			{/if}

			{#if selectedAuthMethod === 'passkey'}
				<div class="flex flex-col items-center">
					<p class="mb-4 text-center">
						Your device will prompt you to use your saved passkey (e.g., via fingerprint, face ID,
						or PIN).
					</p>
					<button class="btn btn-primary" onclick={handlePasskeyAuth} disabled={isLoading}>
						{#if isLoading}
							<span class="loading loading-spinner"></span>
							Authenticating...
						{:else}
							<IconKey class="mr-2" /> Authenticate with Passkey
						{/if}
					</button>
				</div>
			{:else if selectedAuthMethod === 'password'}
				<RuneForm form={passwordForm}>
					{#snippet children(form)}
						<form
							class="space-y-4"
							onsubmit={(e) => {
								e.preventDefault();
								handlePasswordSubmit({ password: form.values.password });
							}}
						>
							<Field name="password">
								{#snippet children(field)}
									<div>
										<label class="floating-label">
											<input
												id="reauth-password"
												name="password"
												type="password"
												class="input input-bordered w-full"
												placeholder="••••••••"
												bind:value={field.value}
												onblur={field.handleBlur}
												required
											/>
											<span>Current Password</span>
										</label>
										<Errors name="password" />
									</div>
								{/snippet}
							</Field>
							{#if errorMessage}
								<div class="alert alert-error text-sm">{errorMessage}</div>
							{/if}
							<div class="modal-action mt-4">
								<button type="button" class="btn" onclick={handleCancel} disabled={isLoading}
									>Cancel</button
								>
								<button type="submit" class="btn btn-primary" disabled={isLoading || !form.isDirty}>
									{#if isLoading}
										<span class="loading loading-spinner"></span>
										Verifying...
									{:else}
										Confirm
									{/if}
								</button>
							</div>
						</form>
					{/snippet}
				</RuneForm>
			{/if}
		</div>
		<button class="modal-backdrop" onclick={handleCancel} aria-label="Close modal"></button>
	</div>
{/if}
