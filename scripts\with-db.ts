#!/usr/bin/env tsx
import 'dotenv/config';
import { spawn } from 'child_process';

/**
 * DB launcher wrapper.
 * Selects the correct POSTGRES_URL based on DB_MODE and runs the given command.
 *
 * Usage examples:
 *   tsx scripts/with-db.ts -- vite dev --host
 *   tsx scripts/with-db.ts -- drizzle-kit studio
 *   DB_MODE=embedded tsx scripts/with-db.ts -- tsx src/lib/server/db/migrate.ts
 */

function resolveUrl() {
  const mode = (process.env.DB_MODE || 'docker').toLowerCase();
  const docker = process.env.POSTGRES_URL_DOCKER || 'postgres://postgres:hairloomdev@localhost:5451/hairloom';
  const embedded = process.env.POSTGRES_URL_EMBEDDED || 'postgres://postgres:hairloomdev@localhost:5452/hairloom';
  const remote = process.env.POSTGRES_URL_REMOTE || '';

  if (process.env.POSTGRES_URL) return process.env.POSTGRES_URL; // explicit override wins

  if (mode === 'docker') return docker;
  if (mode === 'embedded') return embedded;
  if (mode === 'remote') {
    if (!remote) {
      console.error('[with-db] DB_MODE=remote but POSTGRES_URL_REMOTE is not set');
      process.exit(1);
    }
    return remote;
  }

  console.warn(`[with-db] Unknown DB_MODE="${mode}", falling back to docker`);
  return docker;
}

function parseCommand(): { cmd: string; args: string[] } {
  const idx = process.argv.indexOf('--');
  const parts = idx >= 0 ? process.argv.slice(idx + 1) : process.argv.slice(2);
  if (parts.length === 0) {
    console.error('Usage: tsx scripts/with-db.ts -- <command> [args...]');
    process.exit(1);
  }
  const [cmd, ...args] = parts;
  return { cmd, args };
}

function main() {
  const { cmd, args } = parseCommand();
  const url = resolveUrl();
  const env = { ...process.env, POSTGRES_URL: url };

  console.log(`[with-db] DB_MODE=${process.env.DB_MODE || 'docker'} -> POSTGRES_URL=${url}`);

  const child = spawn(cmd, args, { stdio: 'inherit', shell: true, env });
  child.on('exit', (code, signal) => {
    if (signal) process.kill(process.pid, signal);
    process.exit(code ?? 0);
  });
}

main();

