test-results
node_modules

# Output
.output
.vercel
.netlify
.wrangler
/.svelte-kit
/build
/.embedded-pg

# OS
.DS_Store
Thumbs.db

# Env
.env
.env.*
!.env.example
!.env.test

# Secrets CLI configuration
**/.secrets-cli.json
**/secrets-cli.config.json

# Augment tool directory
.augment/

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

playwright-report

# Local certificates
certs/

schema-editor/
.vercel
tools/