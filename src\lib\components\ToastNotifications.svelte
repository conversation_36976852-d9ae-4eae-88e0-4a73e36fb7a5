<!--
This component subscribes to the toastStore and displays toast notifications globally.
It uses a fixed number of DOM elements (cards) and projects toast data onto them.
This approach avoids dynamic element creation/destruction, using CSS for visibility and transitions.
The most recent toasts are shown at the top of the stack.
-->
<script lang="ts">
	import { toastStore, type ToastMessage } from '$lib/stores/toastStore';
	import IconCheckCircle from '~icons/icon-park-outline/check-small';
// Success
	import IconCloseCircle from '~icons/icon-park-outline/close-one';
	// Error
	import IconInfoCircle from '~icons/icon-park-outline/info';
	// Info
	import IconAlertTriangle from '~icons/icon-park-outline/caution';
	// Warning
	import IconClose from '~icons/icon-park-outline/close';
	// For manual close button

	const NUM_TOAST_CARDS = 5;
	const TOTAL_CARDS = 6; // 5 toasts + 1 'more' card
	const MAX_VISIBLE_WHEN_STACKED = 3;

	type CardData =
		| (ToastMessage & { isMore: false; isToast: true })
		| { id: string; type: 'more'; message: string; isMore: true; isToast: false }
		| null;

	let toasts = $state<ToastMessage[]>([]);
	let isExpanded = $state(false);

	$effect(() => {
		const unsubscribe = toastStore.subscribe((value) => {
			toasts = value;
		});
		return unsubscribe;
	});

	const cardData = $derived.by<CardData[]>(() => {
		const data: CardData[] = Array(TOTAL_CARDS).fill(null);
		const toastsToShow = toasts.slice(-NUM_TOAST_CARDS).reverse(); // Newest first

		toastsToShow.forEach((toast, i) => {
			data[i] = { ...toast, isMore: false, isToast: true };
		});

		if (toasts.length > NUM_TOAST_CARDS) {
			data[NUM_TOAST_CARDS] = {
				id: 'more-toast',
				type: 'more',
				message: `${toasts.length - NUM_TOAST_CARDS} more notification(s)...`,
				isMore: true,
				isToast: false
			};
		}
		return data;
	});

	function getIconComponent(type: 'success' | 'error' | 'info' | 'warning' | 'loading' | 'more') {
		switch (type) {
			case 'success':
				return IconCheckCircle;
			case 'error':
				return IconCloseCircle;
			case 'info':
			case 'more':
				return IconInfoCircle;
			case 'warning':
				return IconAlertTriangle;
			default:
				return IconInfoCircle;
		}
	}
</script>

{#if toasts.length > 0}
	<div
		class="toast toast-end toast-bottom z-[1000] p-0"
		style="padding-bottom: 2rem;"
		aria-live="assertive"
		role="group"
	>
		<!-- Accessible toggle for expanding/collapsing the stack -->
		<div class="flex justify-end px-2 pt-2">
			<button
				type="button"
				class="btn btn-ghost btn-xs"
				aria-label={isExpanded ? 'Collapse notifications' : 'Expand notifications'}
				aria-expanded={isExpanded}
				onclick={() => (isExpanded = !isExpanded)}
			>
				{isExpanded ? 'Collapse' : 'Expand'}
			</button>
		</div>
		<div class="stack">
			{#each Array(TOTAL_CARDS) as _, i (i)}
				{@const card = cardData[i]}
				{@const isMoreCard = card?.isMore}
				{@const isToastCard = card?.isToast}

				{@const showMoreCard = !isExpanded && isMoreCard}
				{@const showToastCard = isToastCard && (isExpanded || i < MAX_VISIBLE_WHEN_STACKED)}
				{@const isVisible = showMoreCard || showToastCard}

				{@const offset = i}

				<div
					role="alert"
					class:alert-info={card?.type === 'loading' ||
						card?.type === 'more' ||
						card?.type === 'info'}
					class:alert-success={card?.type === 'success'}
					class:alert-error={card?.type === 'error'}
					class:alert-warning={card?.type === 'warning'}
					class="alert border-base-content/10 flex items-center border pr-2 shadow-lg transition-all duration-300 ease-in-out"
					style={`
                        transform: ${isExpanded ? `translateY(-${i * 68}px)` : `translateY(-${offset * 10}px) scale(${1 - offset * 0.05})`};
                        z-index: ${20 - offset};
                        opacity: ${isVisible ? 1 : 0};
						pointer-events: ${isVisible ? 'auto' : 'none'};
                    `}
				>
					{#if card}
						{@const Icon = getIconComponent(card.type)}
						{#if card.type === 'loading'}
							<span class="loading loading-spinner"></span>
						{:else}
							<Icon class="h-6 w-6 shrink-0" />
						{/if}
						<span class="flex-grow text-sm">{card.message}</span>
						{#if card.isToast}
							<button
								class="btn btn-xs btn-ghost btn-circle ml-2 shrink-0"
								onclick={() => toastStore.remove(card.id as number)}
								aria-label="Close notification"
							>
								<IconClose class="h-4 w-4" />
							</button>
						{/if}
					{/if}
				</div>
			{/each}
		</div>
	</div>
{/if}
