/// <reference types="vitest" />
import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';
import { svelteTesting } from '@testing-library/svelte/vite';
import fs from 'fs';
import path from 'path';
import Icons from 'unplugin-icons/vite';
import { defineConfig, type UserConfig } from 'vite';

export default defineConfig(({ command }) => {
	const config: UserConfig = {
		mode: process.env.MODE,
		plugins: [
			tailwindcss(),
			sveltekit(),
			Icons({
				compiler: 'svelte',
				autoInstall: true
			})
		],
		test: {
			projects: [
				{
					extends: './vite.config.ts',
					plugins: [svelteTesting()],
					test: {
						name: 'client',
						environment: 'jsdom',
						clearMocks: true,
						include: ['src/**/*.svelte.{test,spec}.{js,ts}'],
						exclude: ['src/lib/server/auth/**'],
						setupFiles: ['./vitest-setup-client.ts']
					}
				},
				{
					extends: './vite.config.ts',
					test: {
						name: 'server',
						environment: 'node',
						include: ['src/**/*.{test,spec}.{js,ts}'],
						exclude: ['src/**/*.svelte.{test,spec}.{js,ts}']
					}
				}
			]
		}
	};

	if (command === 'serve') {
		// TLS cert selection: configurable via env
		// DEV_TLS_CERT_BASE without extension, e.g., 'certs/localhost' or 'certs/192-168-2-10.nip.io'
		const TLS_BASE = process.env.DEV_TLS_CERT_BASE || 'certs/localhost';
		const keyPath = path.resolve(__dirname, `${TLS_BASE}-key.pem`);
		const certPath = path.resolve(__dirname, `${TLS_BASE}.pem`);

		config.server = {
			host: true,
			https: fs.existsSync(keyPath) && fs.existsSync(certPath)
				? { key: fs.readFileSync(keyPath), cert: fs.readFileSync(certPath) }
				: undefined
		};
	}

	return config;
});
