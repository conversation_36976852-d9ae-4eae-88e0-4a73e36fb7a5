# SvelteKit App Dockerfile (npm)

# ---- Base ----
FROM node:20-alpine AS base
WORKDIR /app

# ---- Dependencies ----
FROM base AS deps
COPY package.json package-lock.json ./
RUN npm ci

# ---- Builder ----
FROM base AS builder
COPY --from=deps /app/node_modules /app/node_modules
COPY . .
RUN npm run build

# ---- Runner ----
FROM node:20-alpine AS runner
WORKDIR /app
ENV NODE_ENV=production

# Install only production dependencies
COPY package.json package-lock.json ./
RUN npm ci --omit=dev

# Copy the built application artifacts from the builder stage
COPY --from=builder /app/build ./build

# Set the port the SvelteKit app will run on.
ENV PORT=3000
EXPOSE 3000

ENV VITE_WEBAUTHN_ORIGIN=http://localhost:5173
CMD ["node", "build/index.js"]