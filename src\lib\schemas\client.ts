import { z } from 'zod/v4';
import {
    email<PERSON><PERSON>,
    first<PERSON><PERSON><PERSON><PERSON>,
    imageB<PERSON><PERSON><PERSON><PERSON>,
    lastNameField,
    notesField,
    phoneNumberField,
    uuidField
} from './shared-primitives';

/**
 * Base schema for client data with strict validation.
 * Includes all fields for a client record.
 */
export const clientSchema = z.object({
	id: uuidField,
	first_name: first<PERSON><PERSON><PERSON><PERSON>,
	last_name: lastName<PERSON>ield,
	email: emailField.optional().or(z.literal('')),
	phone_number: phoneNumberField,
	notes: notesField,
	image_base64: imageBase<PERSON><PERSON>ield,
	user_id: uuidField,
	created_at: z.date(),
	updated_at: z.date()
});

/**
 * Schema for creating or updating a client.
 * Inherits from the base client schema but makes the `id` optional.
 */
export const clientUpsertSchema = clientSchema.omit({
	id: true,
	user_id: true,
	created_at: true,
	updated_at: true
});

/**
 * Schema for adding a note to a client from a template.
 */
export const clientNoteAddSchema = z.object({
	templateId: z.uuid({ message: 'A template must be selected.' }),
	note_data: z.preprocess(
		(val) => {
			if (typeof val !== 'string' || val === '') return null; // Let Zod handle the type error
			try {
				return JSON.parse(val);
			} catch {
				return null; // Let Zod handle the parse error
			}
		},
		z.record(z.string(), z.unknown()).refine((data) => Object.keys(data).length > 0, {
			message: 'Note data cannot be empty.'
		})
	)
});

// Template for adding a new note to a client
export const createClientNoteTemplate = z.object({
	clientId: z.uuid(),
	note: z.string().min(1, 'Note content cannot be empty')
});

export type CreateClientNoteTemplate = typeof createClientNoteTemplate;
