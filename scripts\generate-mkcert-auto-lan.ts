#!/usr/bin/env tsx
import os from 'os';
import { spawnSync } from 'child_process';

/*
  Auto-detect LOCAL (RFC1918) LAN IPv4 and generate mkcert cert for <ip>.nip.io
  Includes a --with-localhost option to add localhost SANs.

  Usage:
    tsx scripts/generate-mkcert-auto-lan.ts
    tsx scripts/generate-mkcert-auto-lan.ts --with-localhost
*/

function hasMkcert(): boolean {
  const res = spawnSync('mkcert', ['-help'], { stdio: 'pipe' });
  return res.status === 0;
}

function isPrivateIPv4(addr: string): boolean {
  // RFC1918 ranges: 10.0.0.0/8, **********/12, ***********/16
  if (addr.startsWith('10.')) return true;
  if (addr.startsWith('192.168.')) return true;
  const octets = addr.split('.').map((x) => parseInt(x, 10));
  if (octets[0] === 172 && octets[1] >= 16 && octets[1] <= 31) return true;
  return false;
}

function getLocalLanIP(): string | null {
  const interfaces = os.networkInterfaces();
  const candidates: string[] = [];
  for (const name of Object.keys(interfaces)) {
    const addrs = interfaces[name] || [];
    for (const a of addrs) {
      if (
        a && a.address && a.family === 'IPv4' && !a.internal &&
        isPrivateIPv4(a.address) &&
        !a.address.startsWith('169.254.')
      ) {
        candidates.push(a.address);
      }
    }
  }
  // Prefer 192.168.* then 10.* then 172.16-31.* for stability
  const pick = (prefix: (s: string) => boolean) => candidates.find(prefix);
  return (
    pick((s) => s.startsWith('192.168.')) ||
    pick((s) => s.startsWith('10.')) ||
    candidates.find((s) => s.startsWith('172.')) ||
    null
  );
}

function main() {
  const withLocalhost = process.argv.includes('--with-localhost');

  if (!hasMkcert()) {
    console.error('mkcert is not installed. See https://github.com/FiloSottile/mkcert#installation');
    process.exit(1);
  }

  const ip = getLocalLanIP();
  if (!ip) {
    console.error('Could not find a LOCAL (RFC1918) IPv4 address. Connect to a LAN and try again.');
    process.exit(1);
  }

  const domain = `${ip.replace(/\./g, '-')}.nip.io`;
  const args = ['scripts/generate-mkcert.ts', '--lan', ip];
  if (withLocalhost) args.push('--with-localhost');

  console.log(`Detected local LAN IP: ${ip}. Generating cert for ${domain}`);
  const res = spawnSync('tsx', args, { stdio: 'inherit' });
  process.exit(res.status ?? 1);
}

main();

