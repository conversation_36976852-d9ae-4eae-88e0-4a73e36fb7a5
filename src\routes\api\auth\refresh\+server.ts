import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { setJWTCookies, verifyRefreshToken } from '$lib/server/auth/jwt';
import { eq } from 'drizzle-orm';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async (event) => {
  const refreshCookie = event.cookies.get('refresh_token');
  if (!refreshCookie) {
    return json({ error: 'Missing refresh token' }, { status: 401 });
  }

  try {
    const claims = await verifyRefreshToken(refreshCookie);
    // Validate session exists and is not expired
    const [session] = await db
      .select()
      .from(table.user_sessions)
      .where(eq(table.user_sessions.id, claims.sid));

    if (!session || session.expires_at.getTime() <= Date.now()) {
      return json({ error: 'Session expired' }, { status: 401 });
    }

    // Re-issue fresh tokens
    const { generateAccessToken, generateRefreshToken } = await import('$lib/server/auth/jwt');
    const access = await generateAccessToken(claims.sub as string, claims.sid);
    const refresh = await generateRefreshToken(claims.sub as string, claims.sid, (claims as any).ver ? (claims as any).ver + 1 : 1);
    setJWTCookies(event, access, refresh);
    return json({ success: true });
  } catch (e) {
    console.error('Refresh token error', e);
    return json({ error: 'Invalid refresh token' }, { status: 401 });
  }
};

