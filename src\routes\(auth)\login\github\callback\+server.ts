import { createSession, generateSessionToken, setSessionTokenCookie } from '$lib/server/auth';
import { github } from '$lib/server/auth/github';
import { issueSessionJWTs } from '$lib/server/auth/jwt';
import { db } from '$lib/server/db';
import { user_oauth_accounts, users } from '$lib/server/db/schema';
import { error, redirect } from '@sveltejs/kit';
import { OAuth2RequestError } from 'arctic';
import { eq } from 'drizzle-orm';

import type { RequestHandler } from './$types';

export const GET: RequestHandler = async (event) => {
	if (!github) {
		throw error(500, 'GitHub login is not configured.');
	}

	const { url, cookies } = event;
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const storedState = cookies.get('github_oauth_state');

	if (!code || !state || !storedState || state !== storedState) {
		return new Response(null, { status: 400 });
	}

	let tokens, githubUser, existingAccount, userId;

	try {
		tokens = await github.validateAuthorizationCode(code);
		const githubUserResponse = await fetch('https://api.github.com/user', {
			headers: {
				Authorization: `Bearer ${tokens.accessToken()}`
			}
		});
		githubUser = await githubUserResponse.json();

		existingAccount = await db.query.user_oauth_accounts.findFirst({
			where: eq(user_oauth_accounts.provider_user_id, githubUser.id.toString())
		});
	} catch (e) {
		if (e instanceof OAuth2RequestError) {
			// bad verification code, should likely be handled
			return new Response(null, {
				status: 400
			});
		}
		console.error(e);
		return new Response(null, {
			status: 500
		});
	}

	if (existingAccount) {
		const sessionToken = generateSessionToken();
		const session = await createSession(sessionToken, existingAccount.user_id);
		setSessionTokenCookie(event, sessionToken, session.expires_at);
		await issueSessionJWTs(event, existingAccount.user_id, session.id);
		redirect(302, '/app');
	}

	try {
		const existingUser = await db.query.users.findFirst({
			where: eq(users.email, githubUser.email)
		});

		if (existingUser) {
			userId = existingUser.id;
		} else {
			const [newUser] = await db
				.insert(users)
				.values({
					email: githubUser.email,
					first_name: githubUser.name,
					email_verified: true
				})
				.returning({ id: users.id });
			userId = newUser.id;
		}

		await db.insert(user_oauth_accounts).values({
			provider: 'github',
			provider_user_id: githubUser.id.toString(),
			user_id: userId,
			email: githubUser.email
		});
	} catch (e) {
		console.error(e);
		return new Response(null, {
			status: 500
		});
	}

	const sessionToken = generateSessionToken();
	const session = await createSession(sessionToken, userId);
	setSessionTokenCookie(event, sessionToken, session.expires_at);
	await issueSessionJWTs(event, userId, session.id);

	redirect(302, '/app');
};
